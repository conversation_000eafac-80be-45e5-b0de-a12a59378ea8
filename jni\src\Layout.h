﻿#include <Funs.h>//结构体变量
#include <ctime> // 用于获取时间
// 添加必要的头文件
#include <fcntl.h>   // 提供 O_WRONLY, O_CREAT 等文件操作标志
#include <unistd.h>  // 提供 open() 等系统调用

// 声明菜单显示状态变量
static bool show_ChildMenu1 = false;
static bool show_ChildMenu2 = true;
static bool show_ChildMenu3 = false;
static bool show_ChildMenu4 = false;


static std::vector<AdvancedItem> highValueItems; // 存储高价值物品
static bool show_high_value_window = true;      // 是否显示高级物品窗口
static float min_high_value = 1000;             // 最低价值（默认≥1000算高价值）
static float max_high_distance = 200;           // 最远距离（默认≤200M）

// 声明主题选择索引变量
static int style_idx = 1;  // 默认选择第一个主题


bool DrawSwitch[20];
float Nums[20];
void MyFun::Layout_Main() {
    {
    

ImGuiStyle *style = &ImGui::GetStyle();
ImGuiStyle& Style = ImGui::GetStyle();
static int num = 0;
ImGui::Begin("superstar-DFM v1.1");
if (num == 0) {
num = 1;
ImGui::PushStyleVar(ImGuiStyleVar_WindowPadding, {25, 25});
ImGui::SetWindowSize({730,530});
}

if (ImGui::BeginChild("##左侧菜单标题", ImVec2(130, 0), false, ImGuiWindowFlags_NoScrollbar | ImGuiWindowFlags_NavFlattened));
{
if (ImGui::Button("绘制", ImVec2(130,50)))
{
show_ChildMenu1 = true;
show_ChildMenu2 = false;
show_ChildMenu3 = false;
show_ChildMenu4 = false;
}
if (ImGui::Button("信息", ImVec2(130,50)))
{
show_ChildMenu1 = false;
show_ChildMenu2 = true;
show_ChildMenu3 = false;
show_ChildMenu4 = false;
}
if (ImGui::Button("设置", ImVec2(130,50)))
{
show_ChildMenu1 = false;
show_ChildMenu2 = false;
show_ChildMenu3 = true;
show_ChildMenu4 = false;
}
ImGui::Text("FPS %.1f",ImGui::GetIO().Framerate);
ImGui::EndChild();
ImGui::PopStyleVar(1);
ImGui::SameLine();
ImGui::SeparatorEx(ImGuiSeparatorFlags_Vertical);
ImGui::SameLine();
ImGui::PushStyleVar(ImGuiStyleVar_WindowPadding, {25, 25});

if (show_ChildMenu1)
{
if (ImGui::BeginChild("##绘制", ImVec2(0, 0), false, ImGuiWindowFlags_NavFlattened));
{
             

ImGui::ItemSize(ImVec2(0, 4));
ImGui::ItemSize(ImVec2(0, 2));
ImGui::TextColored(ImColor(0, 255, 255,155),"加载耗时 %.3f ms/帧率 %.1f FPS", 1000.0f / ImGui::GetIO().Framerate, ImGui::GetIO().Framerate);


ImGui::Separator();
if (ImGui::CollapsingHeader("绘制人物"))
{
    if (ImGui::Combo("主题颜色", &style_idx, "白色主题\0蓝色主题\0紫色主题\0"))
    {
        switch (style_idx)
        {
        case 0: ImGui::StyleColorsLight(); break;
        case 1: ImGui::StyleColorsDark(); break;
        case 2: ImGui::StyleColorsClassic(); break;
        }
    }
    if (style_idx == 0) {
        ImGui::StyleColorsLight();
    }
    if (style_idx == 1) {
        ImGui::StyleColorsDark();
    }
    if (style_idx == 2) {
        ImGui::StyleColorsClassic();
    }
    if (ImGui::Button("一键开启", ImVec2(-1, 75)))
    {
                ptr.方框 = true;
                ptr.骨骼 = true;
                ptr.射线 = true;
                ptr.头甲 = true;
                ptr.距离 = true;
                ptr.血量 = true;
                ptr.名字 = true;
                ptr.手持 = true;
                ptr.人机 = true;
                ptr.盒子 = true;
    }

             
                ImGui::SliderInt("物资等级过滤", &ptr.物资, 1, 6);
                ImGui::Checkbox("绘制方框", &ptr.方框);
                ImGui::SameLine();
                ImGui::Checkbox("绘制骨骼", &ptr.骨骼);
                ImGui::SameLine();
                ImGui::Checkbox("绘制射线", &ptr.射线);
                //  ImGui::SameLine();
                ImGui::Checkbox("绘制头甲", &ptr.头甲);
                ImGui::SameLine();
                ImGui::Checkbox("绘制距离", &ptr.距离);
                ImGui::SameLine();
                ImGui::Checkbox("绘制血量", &ptr.血量);
                //    ImGui::SameLine();
                ImGui::Checkbox("绘制名字", &ptr.名字);
                ImGui::SameLine();
                ImGui::Checkbox("绘制手持", &ptr.手持);
                ImGui::SameLine();
                ImGui::Checkbox("绘制人机", &ptr.人机);          
                ImGui::Checkbox("绘制盒子", &ptr.盒子);
           
}



ImGui::EndChild();
}
}

if (show_ChildMenu2)
{
if (ImGui::BeginChild("##追踪", ImVec2(0, 0), false, ImGuiWindowFlags_NavFlattened));
{

    ImGui::Text("进程ID:%d \n模块地址:0x%lx\n世界:%lX \nGname:%lX\n数量:%d\n 数组%lX", ptr.pid, ptr.libUE4, ptr.Uworld, ptr.libUE4, ptr.Count, ptr.Arrayaddr);


ImGui::ItemSize(ImVec2(0, 4));
ImGui::ItemSize(ImVec2(0, 2));
ImGui::TextColored(ImColor(0, 255, 255,155),"加载耗时 %.3f ms/帧率 %.1f FPS", 1000.0f / ImGui::GetIO().Framerate, ImGui::GetIO().Framerate);
ImGui::Separator();

	
ImGui::EndChild();
}
}

if (show_ChildMenu3)
{
if (ImGui::BeginChild("##功能", ImVec2(0, 0), false, ImGuiWindowFlags_NavFlattened));
{

if (ImGui::Button("退出", ImVec2(-1, 75)))
                {
                    //停止imgui的主线程循环以及释放资源
                    settings.stop = false;
                }




ImGui::ItemSize(ImVec2(0, 4));
ImGui::ItemSize(ImVec2(0, 2));
ImGui::TextColored(ImColor(0, 255, 255,155),"加载耗时 %.3f ms/帧率 %.1f FPS", 1000.0f / ImGui::GetIO().Framerate, ImGui::GetIO().Framerate);
ImGui::Separator();


    



ImGui::End();
}
}     		

}

if (!show_high_value_window || highValueItems.empty())
return;

// 设置窗口位置（仍然固定在右上角，但宽度和高度可以自适应）
ImGui::SetNextWindowPos(ImVec2(ImGui::GetIO().DisplaySize.x - 400, 50), ImGuiCond_FirstUseEver);

// 不设置固定大小，让窗口自动调整
ImGui::Begin("高级物品", &show_high_value_window, ImGuiWindowFlags_NoCollapse);

ImGui::Text("稀有物品 (%zu):", highValueItems.size());
ImGui::Separator();

// 计算最长的物品名称，用于自动调整窗口宽度
float max_name_width = 0.0f;
for (const auto& item : highValueItems) {
    float name_width = ImGui::CalcTextSize(item.name.c_str()).x;
    if (name_width > max_name_width) {
        max_name_width = name_width;
    }
}

// 设置合适的窗口宽度（名称宽度 + 价格宽度 + 边距）
float window_width = max_name_width + 150.0f + 20.0f; // 150 是价格区域，20 是边距
ImGui::SetWindowSize(ImVec2(window_width, 0)); // 高度设为 0，让 ImGui 自动计算

// 显示物品列表
for (const auto& item : highValueItems) {
    ImGui::Text("%s", item.name.c_str());
    ImGui::SameLine(max_name_width + 10.0f); // 对齐到名称后的固定位置
    ImGui::Text("%d [%.1fM]", item.value, item.distance);
}

ImGui::End();

}
}




