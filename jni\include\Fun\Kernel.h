#pragma once
#include <unistd.h>
#include <fcntl.h>
#include <string>
#include <vector>
#include <sstream>
#include <cstring>
#include <map>
#include <iostream>
#include <fstream>
#include "driver.h"

// 字符转换类型定义
typedef char UTF8;
typedef unsigned short UTF16;

class Core {
private:
    Driver* driver;
    pid_t current_pid = -1;

public:
    pid_t pid = -1;

    Core() {
        driver = new Driver();
    }

    ~Core() {
        delete driver;
    }

    void initialize(pid_t _pid) {
        this->pid = _pid;
        this->current_pid = _pid;
        if (driver) {
            driver->initpid(_pid);
        }
    }

    // ======= READ =========

    template<typename T>
    T read(uintptr_t addr) const {
        T value{};
        read(addr, &value, sizeof(T));
        return value;
    }

    bool read(uintptr_t addr, void* buffer, size_t size) const {
        if (!driver) return false;
        return driver->read_safe(addr, buffer, size);
    }

    // ======= WRITE ========

    template<typename T>
    bool write(uintptr_t addr, const T& value) const {
        return write(addr, &value, sizeof(T));
    }

    bool write(uintptr_t addr, const void* buffer, size_t size) const {
        if (!driver) return false;
        return driver->write(addr, const_cast<void*>(buffer), size);
    }
};

inline Core Core;

typedef char PACKAGENAME;
pid_t pid;

int getPID(char* PackageName) {
    Driver temp_driver;
    pid = temp_driver.get_pid(PackageName);
    if (pid > 0) {
        Core.initialize(pid);
    }
    return pid;
}

uintptr_t GetModuleBaseAddr(const std::string& moduleName) {
    if (pid <= 0) return 0;

    Driver temp_driver;
    temp_driver.initpid(pid);
    return temp_driver.get_module_base(pid, const_cast<char*>(moduleName.c_str()));
}

// ... 其他原有函数保持不变 ...

// 读取字符信息
void getUTF8(UTF8* buf, unsigned long namepy)
{
	UTF16 buf16[16] = { 0 };
	// 直接使用 driver->read_safe() 进行硬件级安全读取
	Driver temp_driver;
	temp_driver.initpid(pid);
	temp_driver.read_safe(namepy, buf16, 28);

	UTF16* pTempUTF16 = buf16;
	UTF8* pTempUTF8 = buf;
	UTF8* pUTF8End = pTempUTF8 + 32;
	while (pTempUTF16 < pTempUTF16 + 28)
	{
		if (*pTempUTF16 <= 0x007F && pTempUTF8 + 1 < pUTF8End)
		{
			*pTempUTF8++ = (UTF8)*pTempUTF16;
		}
		else if (*pTempUTF16 >= 0x0080 && *pTempUTF16 <= 0x07FF && pTempUTF8 + 2 < pUTF8End)
		{
			*pTempUTF8++ = (*pTempUTF16 >> 6) | 0xC0;
			*pTempUTF8++ = (*pTempUTF16 & 0x3F) | 0x80;
		}
		else if (*pTempUTF16 >= 0x0800 && *pTempUTF16 <= 0xFFFF && pTempUTF8 + 3 < pUTF8End)
		{
			*pTempUTF8++ = (*pTempUTF16 >> 12) | 0xE0;
			*pTempUTF8++ = ((*pTempUTF16 >> 6) & 0x3F) | 0x80;
			*pTempUTF8++ = (*pTempUTF16 & 0x3F) | 0x80;
		}
		else
		{
			break;
		}
		pTempUTF16++;
	}
}



bool file_exists(const char* path) {
	return access(path, F_OK) == 0;
}

bool IsHighValueItem(int value, float distance) {
    return value >= min_high_value && distance <= max_high_distance;
}


void 绘制字体描边(float size, int x, int y, ImVec4 color, const char* str)
{
	ImGui::GetBackgroundDrawList()->AddText(NULL, size, ImVec2(x + 1, y), ImGui::ColorConvertFloat4ToU32(ImVec4(0.0f, 0.0f, 0.0f, 1.0f)), str);
	ImGui::GetBackgroundDrawList()->AddText(NULL, size, ImVec2(x - 0.1, y), ImGui::ColorConvertFloat4ToU32(ImVec4(0.0f, 0.0f, 0.0f, 1.0f)), str);
	ImGui::GetBackgroundDrawList()->AddText(NULL, size, ImVec2(x, y + 1), ImGui::ColorConvertFloat4ToU32(ImVec4(0.0f, 0.0f, 0.0f, 1.0f)), str);
	ImGui::GetBackgroundDrawList()->AddText(NULL, size, ImVec2(x, y - 1), ImGui::ColorConvertFloat4ToU32(ImVec4(0.0f, 0.0f, 0.0f, 1.0f)), str);
	ImGui::GetBackgroundDrawList()->AddText(NULL, size, ImVec2(x, y), ImGui::ColorConvertFloat4ToU32(color), str);
}
void 绘制加粗文本(float size, float x, float y, ImColor color, ImColor color1, const char* str)
{
	ImGui::GetBackgroundDrawList()->AddText(
		NULL, size, ImVec2(x - 0.1f, y - 0.1f), color1, str);
	ImGui::GetBackgroundDrawList()->AddText(
		NULL, size, ImVec2(x + 0.1f, y + 0.1f), color1, str);
	ImGui::GetBackgroundDrawList()->AddText(NULL, size, { x, y }, color, str);
}

ImColor 浅蓝 = ImColor(ImVec4(36 / 255.f, 249 / 255.f, 217 / 255.f, 255 / 255.f));
ImColor 蓝色 = ImColor(ImVec4(170 / 255.f, 203 / 255.f, 244 / 255.f, 0.95f));
ImColor 白色 = ImColor(ImVec4(255 / 255.f, 255 / 255.f, 258 / 255.f, 0.95f));
ImColor 浅粉 = ImColor(ImVec4(255 / 255.f, 200 / 255.f, 250 / 255.f, 0.95f));
ImColor 黑色 = ImColor(ImVec4(0 / 255.f, 0 / 255.f, 0 / 255.f, 0.7f));
ImColor 半黑 = ImColor(ImVec4(0 / 255.f, 0 / 255.f, 0 / 255.f, 0.18f));
ImColor 血色 = ImColor(ImVec4(0 / 255.f, 249 / 255.f, 0 / 255.f, 0.35f));
ImColor 红色 = ImColor(ImVec4(233 / 255.f, 55 / 255.f, 51 / 255.f, 0.95f));
ImColor 绿色 = ImColor(ImVec4(50 / 255.f, 222 / 215.f, 50 / 255.f, 0.95f));
ImColor 黄色 = ImColor(ImVec4(255 / 255.f, 255 / 255.f, 0 / 255.f, 0.95f));
ImColor 橘黄 = ImColor(ImVec4(255 / 255.f, 150 / 255.f, 30 / 255.f, 0.95f));
ImColor 粉红 = ImColor(ImVec4(220 / 255.f, 108 / 255.f, 1202 / 255.f, 0.95f));
ImColor 紫色 = ImColor(ImVec4(169 / 255.f, 120 / 255.f, 223 / 255.f, 0.95f));

ImColor 获取等级颜色(int 等级) {
    switch (等级) {
    case 1:  return ImColor(255, 255, 255); // 白色
    case 2:  return ImColor(0, 255, 0);     // 绿色
    case 3:  return ImColor(0, 100, 255);   // 蓝色（更鲜明的蓝色）
    case 4:  return ImColor(128, 0, 255);   // 紫色
    case 5:  return ImColor(255, 215, 0);   // 金色
    case 6:  return ImColor(255, 0, 0);     // 红色
    default: return ImColor(255, 255, 255); // 默认灰色（防越界）
    }
}




void choose_no_background() {
	int choice = -1;
	std::cout << "是否开启无后台模式(1是/0否) ";
	std::cin >> choice;
	if (choice == 0) {

	}
	else if (choice == 1) {
		pid_t pid = fork();
		if (pid > 0) {
			exit(0);
		}
		else if (pid == 0) {

		}
		else {
			fprintf(stderr, "无后台创建成功\n");
			exit(1);
		}
	}
	else if (choice >= 2) {
		exit(1);
	}
	else {
		exit(1);
	}
}
ImColor get物品颜色(int 等级) {
	// 定义颜色映射表（直接在map中构造颜色对象）
	static const std::map<int, ImColor> 颜色映射表 = {
		{1, ImColor(ImVec4(1.0f, 1.0f, 1.0f, 1.0f))},  // 白色
		{2, ImColor(ImVec4(0.0f, 1.0f, 0.0f, 1.0f))},  // 绿色
		{3, ImColor(ImVec4(0.0f, 0.5f, 1.0f, 1.0f))},  // 蓝色
		{4, ImColor(ImVec4(0.5f, 0.0f, 1.0f, 1.0f))}, // 紫色
		{5, ImColor(ImVec4(1.0f, 1.0f, 0.0f, 1.0f))},  // 黄色
		{6, ImColor(ImVec4(1.0f, 0.0f, 0.0f, 1.0f))}   // 红色
	};

	// 查找颜色
	auto it = 颜色映射表.find(等级);
	if (it != 颜色映射表.end()) {
		return it->second;
	}

	// 默认返回白色
	return ImColor(ImVec4(1.0f, 1.0f, 1.0f, 1.0f));
}



inline std::string extractDigits(const int number, const int start, const int end) {
	std::string numStr = std::to_string(number);
	if (numStr[0] == '-') numStr = numStr.substr(1);
	if (start < 0 || end >= numStr.length() || start > end) {
		return "";
	}
	return numStr.substr(start, end - start + 1);
}

inline int getHelmetLevel(const int id) {
	const std::string digits = extractDigits(id, 4, 6);
	static const std::map<std::string, int> helmetLevels = {
		{"900", 1}, {"899", 2}, {"898", 3}, {"897", 4}, {"896", 5}, {"895", 6}
	};
	const auto it = helmetLevels.find(digits);
	return it != helmetLevels.end() ? it->second : 0;
}

std::string getChinese(long int number) {
	static const std::map<long int, std::string> weaponMap = {
		//步枪
	  {830130817, "M4A1"},
	  {830130822, "AKM"},
	  {830130824, "QBZ95-1"},
	  {830130826, "AKS-74U"},
	  {18010000011, "QBZ-17"},
	  {830130828, "ASh-12"},
	  {830130829, "K416"},
	  {830130830, "M16A4"},
	  {830130831, "AUG"},
	  {830130832, "M7"},
	  {830130833, "SG552"},
	  {830130834, "AK-12"},
	  {18010000020, "橙色AK"},
	  {830130837, "SCAR-H"},
	  {18010000022, "CZ805"},
	  {830130839, "G3"},
	  {830130840, "PTR-32"},
	  {830130847, "CAR-15"},
	  {830130853, "AS Val"},
	  {830130854, "腾龙"},
	  //冲锋枪
	  {840130817, "MP5"},
	  {840130818, "P90"},
	  {840130819, "Vector"},
	  {840130820, "Uzi"},
	  {840130821, "野牛"},
	  {840130822, "SMG-45"},
	  {840130824, "SR-3M"},
	  {840130825, "PP19勇士"},
	  {840130826, "MP7"},
	  {840130827,"QCQ171"},
	  //霰弹枪
	  {850130817, "M1014"},
	  {850130818, "S12K"},
	  {850130820, "M870"},
	  //轻机枪
	  {860130820, "QJB210"},
	  {860130817, "PKM"},
	  {860130818, "M249"},
	  {860130819, "XM250"},
	  //射手步枪

	  {870130818, "Mini14"},
	  {870130819, "VSS"},
	  {870130820, "SVD"},
	  {870130821, "M14"},
	  {870130822, "SKS"},
	  {870130823, "SR-25"},
	  {870130824, "SR9"},
	  {870130847, "PSG-1"},
	  //狙击枪
	  {880130823, "SV-98"},
	  {880130824, "R93"},
	  {880130825, "M700"},
	  {880130827, "AWM"},
	  //手枪
	  {890130818, "QSZ92G"},
	  {890130819, ".357左轮"},
	  {890130820, "沙漠之鹰"},
	  {890130821, "G18"},
	  {890130822, "93R"},
	  {18070000007, "CF98-A"},
	  {890130849, "M1911"},
	  {18070000009, "FN57"},
	  {890130826, "G17"},
	  //火炮
	  {18080000002, "RPG-7"},
	  {18080000003, "AT-4"},
	  {18080000006, "AT-4针式导弹发射器"},
	  {18080000007, "RPG-AI专用"},
	  {900130826, "手炮"},
	  {900130827, "虎蹲炮"},
	  //战术道具
	  {18446744073244715154, "致盲烟"},
	  {920130818, "战术匕首"},
	  {920130825,"黑鹰匕首"},
	  {18446744073244715149, "C4"},
	  {18446744073255015143, "烟雾无人机"},
	  {890130846, "治疗枪"},
	  {18446744073244715153, "无人蜂群"},
	  {18446744073244715158, "破片手雷" },
	  {18446744073244715142, "燃烧弹" },
	  {18446744073244715141, "混凝掩体" },
	  {900130825, "鲁鲁炮" },
	  {18446744073244715139, "飞镖" },
	  {18446744073244715137, "破片手雷"},
	  {950130817, "电击箭矢"},
	  {950130818, "侦察箭矢"},
	  {18446744073255015142, "声波陷阱"}
	};

	auto it = weaponMap.find(number);
	return (it != weaponMap.end()) ? it->second : "未知手持";
}


std::string GetAICharacterTagNameById(uint8_t id)
{
    static const std::map<uint8_t, std::string> tagNameMap = {

        { 0,  "无" },
        { 1,  "精英人机" },
        { 2,  "人机" },
        { 3,  "狙击兵" },
        { 4,  "盾兵" },
        { 5,  "重装兵" },
        { 6,  "伞兵" },
        { 7,  "近战兵" },
        { 8,  "火焰兵" },
        { 9,  "火箭兵" },
        { 10, "Boss牢大" },
        { 11, "男团人机" },
        { 12, "鳄鱼" },
        { 13, "非人类" },
        { 14, "直升机驾驶员" },
        { 15, "保险公司" },
        { 16, "活动单位" },
        { 17, "Romytheus 突击" },
        { 18, "Romytheus 持盾" }
    };

    auto it = tagNameMap.find(id);
    if (it != tagNameMap.end()) {
        return it->second;
    }

    // 未找到，返回数字 ID 字符串
    return std::to_string(id);
}


std::string getHeroName(int operatorCode) {
    std::map<int, std::string> operatorMap = {
        {2100654110, "红狼"},
        {2100654105, "威龙"},
        {2100654107, "蜂医"},
        {2100654109, "牧羊人"},
        {2100654108, "露娜"},
        {2100654106, "骇爪"},
        {2100654115, "乌鲁鲁"},
        {2100654116, "蛊" },
        {2100654117, "深蓝"},
        {2100654118, "无名" },
        {2100654119, "疾风" }
    };
    if (operatorMap.find(operatorCode) != operatorMap.end()) {
        return operatorMap[operatorCode];
    }
    else {
        return "未知";
    }
}





std::string getItemName(uint64_t itemID) {
    std::map<uint64_t, std::string> itemMap = {
        {11010006002, "H70 精英头盔"},
        {11010006003, "DICH-9重型头盔"},
        {11010006004, "GT5 指挥官头盔"},
        {11010006007, "H70 夜视精英头盔"},
        {11050006001, "金刚防弹衣"},
        {11050006002, "HA-2防弹装甲"},
        {11050006003, "特里克MAS2.0装甲"},
        {11050006004, "泰坦防弹装甲"},
        {11080006002, "重型登山包"},
        {11080006003, "D7战术背包"},
        {11080006004, "GTO重型战术包"},
        {13110000066, "先进热融合全息瞄准镜"},
        {13110000067, "先进白热成像战斗瞄准镜"},
        {13110000068, "八倍先进热成像狙击镜"},
        {14060000003, "高级头盔维修组合"},
        {14060000004, "高级护甲维修组合"},
        {15010050001, "黄金瞪羚"},
        {15020010031, "强化碳纤维板"},
        {15020010033, "火箭燃料"},
        {15030010012, "摄影机"},
        {15030050001, "显卡"},
        {15030050002, "军用无人机"},
        {15030050004, "军用电台"},
        {15030050007, "笔记本电脑"},
        {15030050008, "刀片服务器"},
        {15030050012, "高速磁盘阵列"},
        {15030050013, "G.T.I卫星通信天线"},
        {15030050014, "飞行记录仪"},
        {15030050017, "军用炮弹"},
        {15030050018, "军用控制终端"},
        {15040050002, "克劳迪乌斯半身像"},
        {15050100008, "变电站技术室"},
        {15050100019, "东楼经理室"},
        {15050200001, "总裁室会客厅"},
        {15050200004, "黑室服务器室"},
        {15050200005, "蓝室核心"},
        {15050200013, "总裁室放映厅"},
        {15050300001, "酒店国王房"},
        {15050300013, "雷达站无人机平台"},
        {15050400001, "地下金库储藏间"},
        {15050400002, "博物馆展厅套间"},
        {15050400005, "老浴场贵宾室"},
        {15050400006, "旅店用餐间"},
        {15050400013, "总裁会议室"},
        {15060040004, "呼吸机"},
        {15060080015, "奥莉薇娅香槟"},
        {15070040003, "实验数据"},
        {15070050001, "量子存储"},
        {15080040001, "棘龙爪化石"},
        {15080050003, "滑膛枪展品"},
        {15080050006, "非洲之心"},
        {15080050010, "万足金条"},
        {15080050014, "赛伊德的怀表"},
        {15080050030, "曼德尔超算单元"},
        {15080050031, "便携军用雷达"},
        {15080050032, "军用信息终端"},
        {15080050040, "主战坦克模型"},
        {15080050041, "步战车模型"},
        {15080050042, "名贵机械表"},
        {15080050044, "绝密服务器"},
        {15080050058, "自动体外除颤器"},
        {15080050061, "云存储阵列"},
        {15080050066, "扫拖一体机器人"},
        {15080050067, "强力吸尘器"},
        {15080050069, "高级咖啡豆"},
        {15080050097, "复苏呼吸机"},
        {15080050098, "微型反应炉"},
        {15080050099, "装甲车电池"},
        {15080050100, "动力电池组"},
        {15080050113, "医疗机械人"},
        {15080050120, "雷斯的留声机"},
        {15080050121, "万金泪冠"},
        {15080050122, "“天圆地方”"},
        {15080050123, "“纵横”"},
        {15080050128, "ECMO"},
        {15080050131, "“钻石”鱼子酱"},
        {15200000034, "乙巳玄武"},
        {15200000049, "炫彩麦小蛋"},
        {15200000058, "幸运木雕"},
        {15200000059, "吴彦祖之镜"},
        {16110000014, "曼德尔砖-棱镜攻势"},
        {16110000017, "曼德尔砖-美杜莎"},
        {16110000018, "曼德尔砖-电玩高手"},
        {16110000019, "曼德尔砖-命运王牌"},
        {37150600001, "6.8x51mm AP"},
        {37170600001, "7.62x51mm M61"},
        {37180600001, "7.62x54R SNB"},
        {37240700001, ".338 Lap Mag AP"},
        {11010005001, "Mask-1铁壁头盔"},
        {11010005002, "H09 防暴头盔"},
        {11010005003, "DICH-1战术头盔"},
        {11010005004, "GN 重型头盔"},
        {11010005009, "GN 重型夜视头盔"},
        {11010005010, "GN 久战重型夜视头盔"},
        {11050005001, "精英防弹背心"},
        {11050005002, "Hvk-2防弹衣"},
        {11050005003, "FS复合防弹衣"},
        {11050005004, "重型突击背心"},
        {11070005001, "飓风战术胸挂"},
        {11070005003, "黑鹰野战胸挂"},
        {11070005004, "DAR突击手胸挂"},
        {11080005001, "ALS背负系统"},
        {11080005002, "HLS-2重型背包"},
        {11080005003, "D3战术登山包"},
        {11080005004, "GT5野战背包"},
        {14020000006, "战地医疗箱"},
        {14060000001, "精密头盔维修包"},
        {14060000002, "精密护甲维修包"},
        {15020010036, "燃料电池"},
        {15030040001, "镜头"},
        {15030040003, "CPU"},
        {15030040013, "手机"},
        {15030050003, "可编程处理器"},
        {15030050006, "数码相机"},
        {15030050019, "军用炸药"},
        {15040010019, "E型滤毒罐"},
        {15040010023, "移动电缆"},
        {15050100013, "西楼调控房"},
        {15050100014, "西楼监视室"},
        {15050100016, "设备领用室"},
        {15050200002, "东区吊桥"},
        {15050200003, "蓝室数据中心"},
        {15050300005, "酒店王子房"},
        {15050300008, "酒店黑桃房"},
        {15050300011, "酒店方片房"},
        {15050300014, "雷达站控制室"},
        {15050300015, "雷达站数据中心"},
        {15050300019, "实验楼资料室"},
        {15050300020, "实验楼办公室"},
        {15050300032, "酒店将军房"},
        {15050400003, "博物馆废弃展厅"},
        {15050400004, "博物馆监控室"},
        {15050400011, "1号审讯室"},
        {15050400014, "巴别塔供电权限卡"},
        {15050400015, "生物数据机房"},
        {15050400016, "Relink植入手术室"},
        {15050400017, "医疗会议室"},
        {15050400018, "门诊室"},
        {15060040001, "静脉定位器"},
        {15060040003, "血氧仪"},
        {15060080014, "咖啡"},
        {15060080011, "可乐"},
        {15070040001, "资料：设计图纸"},
        {15080050002, "纯金打火机"},
        {15080050004, "功绩勋章"},
        {15080050009, "亮闪闪的海盗金币"},
        {15080050013, "雷斯的乐谱本"},
        {15080050015, "阿萨拉特色酒杯"},
        {15080050025, "高级子弹生产零件"},
        {15080050033, "军用卫星通讯仪"},
        {15080050034, "单反相机"},
        {15080050035, "军用望远镜"},
        {15080050062, "阵列服务器"},
        {15080050063, "哈夫克机密档案"},
        {15080050071, "盒装挂耳咖啡"},
        {15080050082, "本地特色首饰"},
        {15080050083, "座钟"},
        {15080050088, "大型电台"},
        {15080050089, "军用弹道计算机"},
        {15080050090, "高速固态硬盘"},
        {15080050092, "卫队金扳指"},
        {15080050093, "优秀雇员奖杯"},
        {15080050094, "赛伊德的手弩"},
        {15080050095, "脑机控制端子"},
        {15080050101, "固体燃料"},
        {15080050115, "体内除颤器"},
        {15080050118, "紫外线灯"},
        {15080050126, "卫星电话"},
        {15080050129, "心脏支架"},
        {15080050132, "“蓝宝石”龙舌兰"},
        {15080050133, "珠宝头冠"},
        {15080050135, "荷美尔陶俑"},
        {15090010040, "机械工蜂模型"},
        {15200000031, "脑机relink医疗报告"},
        {15200000032, "心灵感应.魔方"},
        {15200000048, "麦小蛋"},
        {37100500001, "5.56x45mm M995"},
        {37110500001, "7.62x39mm AP"},
        {37120500001, "5.45x39mm BS"},
        {37130500001, "5.8x42mm DVC12"},
        {37140500001, "9x39mm BP"},
        {37150500001, "6.8x51mm Hybrid"},
        {37160500001, "12.7x55mm PS12B"},
        {37170500001, "7.62x51mm M62"},
        {37180500001, "7.62x54R BT"},
        {37210500001, "5.7x28mm SS190"},
        {37260500001, "4.6x30mm AP SX"},
        {37280500001, ".300BLK"},
        {11010004001, "D6 战术头盔"},
        {11010004002, "MHS 战术头盔"},
        {11010004003, "DICH 训练头盔"},
        {11010004004, "GT1 战术头盔"},
        {11050004001, "武士防弹背心"},
        {11050004002, "突击手防弹背心"},
        {11050004003, "DT-AVS防弹衣"},
        {11050004004, "MK-2战术背心"},
        {11050004007, "HMP特勤防弹衣"},
        {11070004001, "强袭战术背心"},
        {11070004002, "突击者战术背心"},
        {11070004003, "DRC先进侦察胸挂"},
        {11070004004, "GIR野战胸挂"},
        {11080004001, "MAP侦察背包"},
        {11080004002, "野战徒步背包"},
        {11080004003, "D2战术登山包"},
        {11080004004, "GT1户外登山包"},
        {11080004005, "穿山甲通用战术包"},
        {13020000173, "AR特勤一体消音组合"},
        {13020000355, "AKM性能枪管组合"},
        {13020000362, "K416特勤短枪管组合"},
        {13020000366, "AUG晨零一体消音管"},
        {13020000378, "ASh-12 歼灭高精长枪管"},
        {13020000379, "ASh-12 CQB短枪管"},
        {13020000383, "M7堤风超长枪管组合"},
        {13020000396, "SR25新星超长枪管"},
        {13020000397, "SR25追风长枪管"},
        {13020000398, "SR25瞬息短枪管"},
        {13020000406, "SKS截断标准枪管"},
        {13020000407, "SKS瞬息超长枪管"},
        {13020000410, "VSS海啸长枪管组合"},
        {13020000420, "Vector堡垒重枪管组合"},
        {13020000421, "Vector长剑超长枪管组合"},
        {13020000441, "AWM天际线长枪管"},
        {13020000443, "M700一体消音管"},
        {13020000449, "S12K防御者短枪管组合"},
        {13020000451, "S12K破阵长枪管组合"},
        {13020000474, "MP5SD特勤一体消音枪管"},
        {13020000492, "K416A8枪管组合"},
        {13020000493, "K416A8长枪管组合"},
        {13020000498, "MP7蜂刺长枪管组合"},
        {13020000500, "MP7狼牙轻枪管"},
        {13020000512, "G3平台神射枪管组合"},
        {13020000518, "M7灵蜥短枪管"},
        {13020000520, "M250侍卫短枪管"},
        {13020000524, "M250钛金长枪管"},
        {13020000529, "QJB201新式獠牙短枪管"},
        {13020000530, "QJB201新式重锤战术枪管"},
        {13020000531, "QCQ171新式狡兔短枪管"},
        {13020000532, "QCQ171新式红缨战术枪管"},
        {13020000533, "QCQ171新式渗透一体消音枪管"},
        {13020000541, "K437断刃短枪管组合"},
        {13020000543, "K437长矛手长枪管组合"},
        {13020000546, "K437特攻一体消音枪管"},
        {13020000550, "725双管霰弹枪鹰隼长枪管"},
        {13030000106, "G3后握把"},
        {13030000111, "幻影后握把"},
        {13030000112, "侵袭后握把"},
        {13030000115, "禁区一体枪托"},
        {13030000124, "svd聚合物一体枪托"},
        {13030000125, "VSS精英一体枪托"},
        {13030000164, "MP7平衡后握把"},
        {13030000165, "MP7稳固后握把"},
        {13030000166, "AR重塔握把"},
        {13030000167, "AK重塔握把"},
        {13030000168, "AK镂空握把"},
        {13030000169, "RK3后握把"},
        {13030000170, "天蝎座后握把"},
        {13030000172, "G3镂空握把"},
        {13030000178, "共振二代一体枪托"},
        {13030000180, "新式尖兵轻型握把"},
        {13030000181, "新式侍剑重型握把"},
        {13040000007, "影袭导轨枪托"},
        {13040000110, "UR特种战术枪托"},
        {13040000111, "416-C伸缩枪托"},
        {13040000118, "PT1特种枪托"},
        {13040000121, "ASh-12骨架枪托"},
        {13040000122, "ASh-12狙击枪托"},
        {13040000123, "Vector共振一体式枪托"},
        {13040000137, "M249精英骨架枪托"},
        {13040000181, "PT3死士枪托"},
        {13040000183, "MRGS镂空枪托"},
        {13040000184, "影袭托芯枪托"},
        {13040000201, "725双管霰弹枪州长短枪托"},
        {13040000202, "725双管霰弹枪守望者枪托"},
        {13050000254, "SKS先进护木"},
        {13050000325, "M249脚架护木"},
        {13110000051, "HAMR组合瞄准镜"},
        {13110000052, "侦察1.5/5可调瞄准镜"},
        {13110000056, "光学狙击8倍瞄准镜"},
        {13110000057, "3/7可调倍率狙击镜"},
        {13110000069, "LPVO多倍率战斗瞄具"},
        {13110000070, "6/12神射手变倍狙击镜"},
        {13110000072, "1p-29俄制3倍瞄准镜"},
        {13110000073, "视点3倍瞄准镜"},
        {13110000076, "侧置全景红点瞄准镜"},
        {13110000077, "侧置XRO快速反应瞄准镜"},
        {13110000078, "侧置微型红点瞄准镜"},
        {13110000079, "侧置战斗红点瞄准镜"},
        {13110000082, "M157火控光学系统"},
        {13110000088, "侧置Osight微型瞄准镜"},
        {13110000091, "灵眼3/7测距狙击瞄准镜"},
        {13110000092, "灵眼6/12测距狙击瞄准镜"},
        {13120000049, "M14 30发弹匣"},
        {13120000150, "SR25 20发弹匣"},
        {13120000254, "VSS 45发弹匣"},
        {13120000260, "ASh-12扩容30发弹匣"},
        {13120000263, "M7 45发6.8弹鼓"},
        {13120000272, "Vector扩容40发弹匣"},
        {13120000277, "MP5 50发弹鼓"},
        {13120000307, "勇士45发扩容弹匣"},
        {13120000314, "R93 15发弹匣"},
        {13120000319, "MP7 30发弹匣"},
        {13120000320, "MP7 40发弹匣"},
        {13120000321, "MP7 60发弹鼓"},
        {13120000331, "SR25 30发扩容弹匣"},
        {13120000333, "AR 60发扩容弹匣"},
        {13120000341, "新式45发扩容弹匣"},
        {13120000345, "45发 K437扩容弹匣"},
        {13120000348, "K437 60发弹鼓"},
        {13130000159, "炽火抑制器"},
        {13130000160, "钛金竞赛制退器"},
        {13130000164, "轻语战术消音器"},
        {13130000165, "海神消焰器"},
        {13130000166, "泰坦补偿器"},
        {13130000167, "堡垒水平补偿器"},
        {13130000168, "沙暴垂直补偿器"},
        {13130000169, "死寂消音器"},

        //收集品
        {15010050001, "黄金瞪羚"},
        {15030050007, "笔记本电脑"},
        {15030050004, "军用电台"},
        {15030050008, "刀片服务器"},
        {15020010031, "强化碳纤维板"},
        {15030010012, "摄影机"},
        {15030050001, "显卡"},
        {15020010033, "火箭燃料"},
        {15030050002, "军用无人机"},
        {15070040003, "实验数据"},
        {15080050003, "滑膛枪展品"},
        {15030050012, "高速磁盘阵列"},
        {15030050014, "飞行记录仪"},
        {15030050013, "G.T.I卫星通信天线"},
        {15040050002, "克劳迪乌斯半身像"},
        {15080040001, "棘龙爪化石"},
        {15030050017, "军用炮弹"},
        {15070050001, "量子存储"},
        {15060040004, "呼吸机"},
        {15060080015, "奥莉薇娅香槟"},
        {15030050018, "军用控制终端"},
        {15080050058, "自动体外除颤器"},
        {15080050041, "步战车模型"},
        {15080050044, "绝密服务器"},
        {15080050042, "名贵机械表"},
        {15080050030, "曼德尔超算单元"},
        {15080050014, "赛伊德的怀表"},
        {15080050031, "便携军用雷达"},
        {15080050010, "万足金条"},
        {15080050006, "非洲之心"},
        {15080050032, "军用信息终端"},
        {15080050040, "主战坦克模型"},
        {15080050061, "云存储阵列"},
        {15080050100, "动力电池组"},
        {15080050121, "万金泪冠"},
        {15080050099, "装甲车电池"},
        {15080050120, "雷斯的留声机"},
        {15080050067, "强力吸尘器"},
        {15080050066, "扫拖一体机器人"},
        {15080050113, "医疗机械人"},
        {15080050069, "高级咖啡豆"},
        {15080050098, "微型反应炉"},
        {15080050122, "“天圆地方”"},
        {15080050097, "复苏呼吸机"},
        {15080050123, "“纵横”"},
        {15030050006, "数码相机"},
        {15060040001, "静脉定位器"},
        {15030040001, "镜头"},
        {15040010019, "E型滤毒罐"},
        {15020010036, "燃料电池"},
        {15030050003, "可编程处理器"},
        {15060040003, "血氧仪"},
        {15030040013, "手机"},
        {15030050019, "军用炸药"},
        {15030040003, "CPU"},
        {15040010023, "移动电缆"},
        {15080050004, "功绩勋章"},
        {15080050035, "军用望远镜"},
        {15070040001, "资料：设计图纸"},
        {15080050033, "军用卫星通讯仪"},
        {15080050062, "阵列服务器"},
        {15080050025, "高级子弹生产零件"},
        {15080050063, "哈夫克机密档案"},
        {15080050034, "单反相机"},
        {15080050009, "亮闪闪的海盗金币"},
        {15080050015, "阿萨拉特色酒杯"},
        {15080050095, "脑机控制端子"},
        {15080050092, "卫队金扳指"},
        {15080050115, "体内除颤器"},
        {15080050083, "座钟"},
        {15080050082, "本地特色首饰"},
        {15080050071, "盒装挂耳咖啡"},
        {15080050101, "固体燃料"},
        {15080050089, "军用弹道计算机"},
        {15080050088, "大型电台"},
        {15080050093, "优秀雇员奖杯"},
        {15080050090, "高速固态硬盘"},
        {15080050094, "赛伊德的手弩"},
        {15090010040, "机械工蜂模型"},
        {15030040007, "ASOS电脑主板"},
        {15030040005, "固态硬盘"},
        {15020050003, "聚乙烯纤维"},
        {15020050001, "自旋型手锯"},
        {15020050004, "防弹陶瓷"},
        {15020010024, "高出力粉碎钳"},
        {15020010035, "高级燃料"},
        {15030040006, "内存条"},
        {15020050006, "钛合金"},
        {15080050118, "紫外线灯"},
        {15020050005, "特种钢"},
        {15030050011, "GS5 手柄"},
        {15060080008, "清新橘味能量凝胶"},
        {15030040014, "电子干扰器"},
        {15040040007, "电动车电池"},
        {15060080005, "生津柠檬茶"},
        {15030050005, "HIFI声卡"},
        {15040050003, "已损坏的热像仪"},
        {15040010015, "间谍笔"},
        {15030040012, "收音机"},
        {15070050002, "资料：军事情报"},
        {15060040002, "血压仪"},
        {15040010014, "阿萨拉风情水壶"},
        {15080040002, "海盗弯刀"},
        {15080050001, "后妃耳环"},
        {15080050057, "无菌敷料包"},
        {15080050018, "黄金饰章"},
        {15080050016, "阿萨拉特色酒壶"},
        {15080050045, "加密路由器"},
        {15080050024, "中级子弹生产零件"},
        {15080050043, "仪典匕首"},
        {15080040003, "犄角墙饰"},
        {15080050036, "军用热像仪"},
        {15080050038, "专业声卡"},
        {15080050037, "广角镜头"},
        {15080050124, "阿萨拉特色提灯"},
        {15080050105, "军用露营灯"},
        {15080050117, "离心机"},
        {15080050070, "三角洲特种部队：黑鹰坠落（盒装版）"},
        {15080050103, "燃气喷灯"},
        {15080050114, "人工膝关节"},
        {15080050068, "胶囊咖啡机套组"},
        {15080050064, "阿萨拉卫队机密档案"},
        {15080050091, "便携音响"},
        {15080050106, "便携生存套组"},
        {15080050102, "信号棒"},
        {15080050119, "植物样本"},
        {15020050008, "继电器"},
        {15020010023, "高精数显卡尺"},
        {15020040001, "工具箱"},
        {15020040002, "芳纶纤维"},
        {15020010028, "燃油"},
        {15020010015, "一包水泥"},
        {15020010034, "低级燃料"},
        {15020010018, "机械破障锤"},
        {15020010037, "燃气罐"},
        {15020010025, "高分子布料"},
        {15020050002, "无线便携电钻"},
        {15020010016, "一桶油漆"},
        {15060010006, "听诊器"},
        {15040010021, "枪械零件"},
        {15040010022, "转换插座"},
        {15030010001, "军用移动电源"},
        {15030040010, "液晶显示屏"},
        {15060010007, "医用酒精"},
        {15060080010, "香喷喷炒面"},
        {15060010004, "额温枪"},

        {15030040002, "摄像头"},
        {15030010008, "音频播放器"},
        {15030050016, "太阳能板"},
        {15040010017, "火药"},
        {15080050021, "阿萨拉时尚周刊"},
        {15070050003, "情报文件"},
        {15080050008, "古怪的海盗银币"},
        {15080050011, "古老的海盗望远镜"},
        {15060080011, "可乐"},
        {15080050012, "“起舞的女郎”挂饰"},
        {15070040002, "资料：商业文件"},
        {15080050023, "初级子弹生产零件"},
        {15070010001, "U盘"},
        {15060080016, "大豆蛋白粉包"},
        {15070010002, "存储卡"},
        {15060080013, "军用罐头"},
        {15080050096, "军情录音"},
        {15080050074, "英式袋泡茶"},
        {15080050065, "骨锯"},
        {15080050075, "木雕烟斗"},
        {15080050027, "三角洲特种部队：刺刀特遣队"},
        {15080050108, "多用途电池"},
        {15080050073, "维生素泡腾片"},
        {15080050072, "摩卡咖啡壶"},
        {15080050076, "糖三角"},
        {15080050107, "狩猎火柴"},
        {15080050039, "电子温度计"},
        {15010010015, "迷你氢电池"},
        {15020010008, "原木木板"},
        {15090010041, "医疗无人机"},
        {15020010019, "尖嘴钳"},
        {15020010007, "电动爆破锤"},
        {15020010005, "螺丝刀"},
        {15080050109, "轻型户外炉具"},
        {15080050116, "电子显微镜"},
        {15020010020, "电笔"},
        {15020010017, "便携液压扳手"},
        {15020010012, "电线"},
        {15010010013, "电火机"},
        {15020040004, "水平仪"},
        {15020010026, "波纹软管"},
        {15030010002, "手机电池"},
        {15020010032, "LED灯管"},
        {15020010021, "角磨机"},
        {15030010007, "DVD光驱"},
        {15020010027, "模拟温度计"},
        {15020040003, "压力计"},
        {15030010013, "印刷电路板"},
        {15030010011, "电源"},
        {15020050007, "电容"},

        {15020010030, "喷漆"},
        {15040010003, "古老的斯芬克斯像"},
        {15040010012, "插座"},
        {15060010001, "盐溶液"},
        {15030040011, "超高频读卡器"},
        {15060010010, "注射器"},
        {15060010003, "小药瓶"},
        {15060010002, "手术镊子"},
        {15030010019, "机械硬盘"},
        {15060080001, "苹果"},
        {15030010018, "风冷散热器"},
        {15060010012, "输液工具"},
        {15030010014, "键盘"},
        {15070010007, "建筑图纸3号"},
        {15060080009, "野外能量棒"},
        {15080010004, "太阳碟"},
        {15080010003, "非洲鼓"},
        {15080040004, "残弹挂坠"},
        {15060080004, "无糖缓释能量棒"},
        {15080040005, "非洲木雕"},
        {15070010005, "建筑图纸1号"},
        {15070010009, "建筑图纸5号"},
        {15070010006, "建筑图纸2号"},
        {15060080006, "纯净水"},
        {15070010008, "建筑图纸4号"},
        {15080050007, "锈迹斑斑的海盗铜币"},
        {15080050079, "当地再制咖啡"},
        {15080050019, "酒店宣传海报"},
        {15080050084, "石工锤"},
        {15080050110, "充电电池组"},
        {15080050022, "阿萨拉娱乐月刊"},
        {15080050077, "调料套组"},
        {15080050080, "强力胶"},
        {15080050078, "袋装咖啡豆"},
        {15080050020, "阿萨拉新闻周刊"},
        {15080050086, "一盒钉子"},
        {15080050085, "羊角锤"},
        {15090010024, "军情照片"},
        {15090010077, "样本试管"},
        {15090910019, "物流信息单"},
        {15080050130, "电动马达"},
        {15080050131, "“钻石”鱼子酱"},
        {15200000059, "吴彦祖之镜"},
        {15200000058, "幸运木雕"},
        {15200000034, "乙巳玄武"},
        {15080050133, "珠宝头冠"},
        {15080050129, "心脏支架"},
        {15080050128, "ECMO"},
        {15080050132, "“蓝宝石”龙舌兰"},
        {15080050135, "荷美尔陶俑"},
        {15200000049, "炫彩麦小蛋"},
        {15080050126, "卫星电话"},
        {15200000032, "心灵感应.魔方"},
        {15200000048, "麦小蛋"},
        {15200000031, "脑机relink医疗报告"},
        {15080050134, "马赛克灯台"},
        {15080050136, "鳄鱼蛋"},
        {15080050127, "生化培养箱"},
        {15200000044, "OLIGHT WARRIOR 3S联名手电"}

    };
    if (itemMap.find(itemID) != itemMap.end()) {
        return itemMap[itemID];
    }
}









#include <cstdint>

void DrawHealthBar(float x, float y, float health, float maxHealth) {
	// 参数校正
	if (health < 0) health = 0;
	if (health > maxHealth) health = maxHealth;

	// 血条尺寸
	const float barWidth = 60.0f;
	const float barHeight = 6.0f;

	// 计算绘制位置（居中显示）
	float startX = x - barWidth / 2.0f;
	float startY = y - 20.0f; // 通常是头顶上方一点

	// 颜色计算：绿色 → 红色
	float ratio = health / maxHealth;
	ImVec4 hpColor = ImVec4(1.0f - ratio, ratio, 0.0f, 0.9f); // 绿色多是满血

	// 背景颜色
	ImVec4 bgColor = ImVec4(0.2f, 0.2f, 0.2f, 0.6f);

	ImDrawList* draw = ImGui::GetBackgroundDrawList();

	// 背景框
	draw->AddRectFilled(
		ImVec2(startX, startY),
		ImVec2(startX + barWidth, startY + barHeight),
		ImGui::ColorConvertFloat4ToU32(bgColor),
		3.0f
	);

	// 血量条
	draw->AddRectFilled(
		ImVec2(startX, startY),
		ImVec2(startX + barWidth * ratio, startY + barHeight),
		ImGui::ColorConvertFloat4ToU32(hpColor),
		3.0f
	);

	// 外框
	draw->AddRect(
		ImVec2(startX, startY),
		ImVec2(startX + barWidth, startY + barHeight),
		ImGui::ColorConvertFloat4ToU32(ImVec4(0, 0, 0, 1)),
		3.0f
	);
}



std::string DecryptAnsiName(uintptr_t address, uint32_t len) {
	std::string name(len, '\0');
	// 直接使用 driver->read_safe() 进行硬件级安全读取
	Driver temp_driver;
	temp_driver.initpid(pid);
	temp_driver.read_safe(address, name.data(), len);

	uint16_t v5 = len;
	uint16_t key = 0;

	switch (v5 % 9) {
	case 0: key = ((v5 & 0x1F) + v5 + 0x80) | 0x7F; break;
	case 1: key = ((v5 ^ 0xDF) + v5 + 0x80) | 0x7F; break;
	case 2: key = ((v5 | 0xCF) + v5 + 0x80) | 0x7F; break;
	case 3: key = (33 * v5 + 0x80) | 0x7F; break;
	case 4: key = (v5 + (v5 >> 2) + 0x80) | 0x7F; break;
	case 5: key = (3 * v5 + 133) | 0x7F; break;
	case 6: key = (((4 * v5) | 5) + v5 + 0x80) | 0x7F; break;
	case 7: key = (((v5 >> 4) | 7) + v5 + 0x80) | 0x7F; break;
	case 8: key = ((v5 ^ 0x0C) + v5 + 0x80) | 0x7F; break;
	default: key = ((v5 ^ 0x40) + v5 + 0x80) | 0x7F; break;
	}
	for (uint32_t i = 0; i < len; i++)
		name[i] ^= key;
	return name;
}


std::string GetName(long int  AddrGNames,uint32_t index)
{
	const auto FNameStride = 0x2;
	const auto FNameEntryToLenBit = 6;
	const auto FNameEntryToString = 0x2;
	const auto GNamesToFNamePool = 0x38;
	uint32_t Block = (index >> 15) & 0x1FFF8;
	uint32_t Offset = FNameStride * index & 0x7FFFE;
	auto FNamePool = AddrGNames + GNamesToFNamePool;

	// 直接使用 driver->read_safe() 进行硬件级安全读取
	Driver temp_driver;
	temp_driver.initpid(pid);

	long int NamePoolChunk;
	temp_driver.read_safe(FNamePool + Block, &NamePoolChunk, sizeof(long int));

	auto FNameEntry = NamePoolChunk + Offset;

	uint16_t FNameEntryHeader;
	temp_driver.read_safe(FNameEntry, &FNameEntryHeader, sizeof(uint16_t));

	auto StrPtr = FNameEntry + FNameEntryToString;
	auto StrLength = FNameEntryHeader >> FNameEntryToLenBit;
	if (StrLength > 0 && StrLength < 250) {
		return DecryptAnsiName(StrPtr, StrLength);
	}
	else {
		return "none";
	}

}