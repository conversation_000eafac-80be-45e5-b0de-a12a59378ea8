#include "draw.h"    //绘制套
#include "AndroidImgui.h"     //创建绘制套
#include "GraphicsManager.h" //获取 当前渲染模式
#include <iostream>//标准库文件
#include "Layout.h"
#include <thread>
#include <Funs.h>//结构体变量
#include <Kernel.h>//读取
#include <vector>
#include <cstdlib>
#include <iostream>

#include <vector>
#include <string>
#include <algorithm>
//#include <nlohmann/json.hpp> // 需要安装 nlohmann/json


MyImGui Initptr;//构建
MyFun Point;

timer* rThreadFPS;//创建渲染线程fps等结构体
timer tempfps;
//float Nums[1] = { 60.0f };  // 默认值

int main() {
	

	std::cout << "请选择渲染方式：\n";
	std::cout << "0  VULKAN渲染（更流畅）\n";
	std::cout << "1  OPENGL渲染（更精致）\n";
	std::cout << "输入选择：";
	std::cin >> ptr.mode;  // 确保 ptr 是 data 类型，且 data 有 int mode 成员
	if (ptr.mode == 1) {  // 使用 == 进行比较，而不是 =
		::graphics = GraphicsManager::getGraphicsInterface(GraphicsManager::OPENGL);

		std::cout << "已设置为：OPENGL渲染模式\n";
	}
	else {
		::graphics = GraphicsManager::getGraphicsInterface(GraphicsManager::VULKAN);

		std::cout << "已设置为：VULKAN渲染模式\n";
	}


	printf("\n先打开游戏\n先打开游戏\n先打开游戏\n\n");
	printf("\n更新地址@wolfdfm\n");


		std::cout << "FPS: " << Nums[0] << std::endl;
		std::cout << "输入你手机支持的帧数FPS 60-144: ";
		std::cin >> Nums[0];  // 手动输入新值
		std::cout << "FPS 设置成功: " << Nums[0] << std::endl;
	
	

	choose_no_background();//无后台
	//vsync_handler();
	
	Point.GetBase();
	float fpsnum;
	//Nums[0] = 120.0f; //此索引用以fps设置,初始化为60帧
	settings.threadtime = &fpsnum;//取地址
	rThreadFPS = &tempfps;//取地址
	::graphics = GraphicsManager::getGraphicsInterface(GraphicsManager::VULKAN);

	//获取屏幕信息    
	::screen_config();
	::native_window_screen_x = (::displayInfo.height > ::displayInfo.width ? ::displayInfo.height : ::displayInfo.width);
	::native_window_screen_y = (::displayInfo.height > ::displayInfo.width ? ::displayInfo.height : ::displayInfo.width);
	::abs_ScreenX = (::displayInfo.height > ::displayInfo.width ? ::displayInfo.height : ::displayInfo.width);
	::abs_ScreenY = (::displayInfo.height < ::displayInfo.width ? ::displayInfo.height : ::displayInfo.width);

	::window = android::ANativeWindowCreator::Create("", native_window_screen_x, native_window_screen_y, permeate_record);
	graphics->Init_Render(::window, native_window_screen_x, native_window_screen_y);

	sleep(1);  // 单位为秒
	std::thread touch_thread(HandleTouchEvent);//调用触摸
	touch_thread.detach();//抛出

	::init_My_drawdata(); //初始化绘制数据


	//EnemyPos = new 二维;//敌人屏幕位置指针,动态分配,程序结束系统会自动回收,否则等着泄露吧老弟
	Aim = new collimation;//自瞄指针,动态分配内存

	//static bool flag = true;
	rThreadFPS->AotuFPS_init();//初始化fps
	rThreadFPS->setAffinity();//设置均和
	while (settings.stop) {
		rThreadFPS->SetFps(Nums[0]);//稳定fps
		drawBegin();
		if (permeate_record == false) {
			android::ANativeWindowCreator::ProcessMirrorDisplay();
		}
		graphics->NewFrame();
		highValueItems.clear();
		Point.Drawing_Main(ImGui::GetForegroundDrawList()); //调用读取与Draws
		Point.Layout_Main(); //调用UI
	

		graphics->EndFrame();
		fpsnum = rThreadFPS->AotuFPS();
	}
	// graphics->DeleteTexture(image);
	graphics->Shutdown();
	android::ANativeWindowCreator::Destroy(::window);
	return 0;
}

int MyFun::GetBase() {

	ptr.pid = getPID("com.tencent.tmgp.dfm");//获取pid

	//判断防止获取错误
	if (ptr.pid > 0 && ptr.pid < 100000)
		Core.initialize(ptr.pid);
	else {
		printf("\n三角洲未启动,请先启动游戏!\n");
		return 0;
	}
	ptr.libUE4 = GetModuleBaseAddr("libUE4.so");
	ptr.Gname = ptr.libUE4 + 0x1adf9540;
	return 1;
}





void MyFun::Drawing_Main(ImDrawList* Draws) {

	ImColor 人物颜色;
	ImColor 头甲颜色;
	in.Number = 0;//初始化总人数
	//获取世界等等数据
	ptr.Gworld = Core.read<uintptr_t>(ptr.libUE4 + 0x1b109978);
	ptr.Uworld = Core.read<uintptr_t>(ptr.Gworld + 0x110) + 0x208; //208
	//获取数量
	ptr.Count = Core.read<int>(ptr.Uworld + 0x8);
	//数组
	ptr.Arrayaddr = Core.read<uintptr_t>(ptr.Uworld);
	//玩家控制器0x1A8) + 0x50) + 0x0) + 0x40);
	ptr.controller = Core.read<uintptr_t>(Core.read<uintptr_t>(Core.read<uintptr_t>(Core.read<uintptr_t>(ptr.Gworld + 0x1A8) + 0x50) + 0x0) + 0x40);

	//自身结构体
	ptr.Oneself = Core.read<uintptr_t>(ptr.controller + 0x408);

	MinimalViewInfo viewInfo;
	//相机组件
	Core.read(Core.read<uintptr_t>(ptr.controller + 0x420) + 0x3560, &viewInfo, sizeof(viewInfo));

	//队伍编号
	int MyTeamID = Core.read<int>(Core.read<uintptr_t>(ptr.Oneself + 0xF00) + 0x118);//11c战场模式

	//开火
 //   ptr.Shoot = Core.read<int>(Core.read<uintptr_t>(ptr.Oneself + 0xE40) + 0x4FE);

	//开镜
//    ptr.zoomin = Core.read<int>(Core.read<uintptr_t>(ptr.Oneself + 0xE40) + 0x342);

	 //获取自身坐标xyz		
	ptr.MyPos = viewInfo.Location;

	

	/*const uint8_t uuid[16] = { 0x10,0x77,0xEF,0xEC,0xC0,0xB2,0x4D,0x02,0xAC,0xE3,0x3C,0x1E,0x52,0xE2,0xFB,0x4B };
	AMediaDrm* drmCheck = AMediaDrm_createByUUID(uuid);

	ImVec2 screenPos = ImVec2(ImGui::GetIO().DisplaySize.x / 6.0f, ImGui::GetIO().DisplaySize.y / 6.0f);

	if (drmCheck) {
		AMediaDrm_setPropertyByteArray(drmCheck, "sessionId", nullptr, 0);
		media_status_t status = AMediaDrm_getPropertyString(drmCheck, "vendor", nullptr);

		if (status == AMEDIA_OK) {
			Draws->AddText(screenPos, IM_COL32(255, 0, 0, 255), "[DRM Session Active]");
		}
		else {
			Draws->AddText(screenPos, IM_COL32(0, 255, 0, 255), "[DRM Ready]");
		}
		AMediaDrm_release(drmCheck);
	}
	else {
		Draws->AddText(screenPos, IM_COL32(255, 255, 0, 255), "[DRM Unavailable]");
	}*/



	//const char* encrypted_file = "/data/user/0/com.tencent.tmgp.dfm/files/ano_tmp/mrpcs_a_cd.data";
	////extern float abs_ScreenX, abs_ScreenY;// 绝对屏幕X _ Y
	//ImVec2 screenPos = ImVec2(ImGui::GetIO().DisplaySize.x / 6.0f, ImGui::GetIO().DisplaySize.y / 6.0f);

	//if (file_exists(encrypted_file)) {
	//	Draws->AddText(screenPos, IM_COL32(255, 0, 0, 255), "[对局已加密,注意演戏]");
	//}
	//else {
	//	Draws->AddText(screenPos, IM_COL32(0, 255, 0, 255), "[对局未加密]");
	//}

	//ImVec2 seenPos(20.0f, 10.0f);  // X=20px, Y=10px（距离左边缘 20px，距离顶部 10px）
	//char buffer[256]; // 确保缓冲区足够大
	//snprintf(buffer, sizeof(buffer),"进程ID:%d 模块地址:0x%lx世界地址:%lX\n读取模式:syscall\n更新地址:@sjzdao666",ptr.pid, ptr.libUE4, ptr.Uworld);
	//Draws->AddText(seenPos, IM_COL32(0, 255, 0, 255), buffer);



	//开始遍历对象数据
	for (int i = 0; i < ptr.Count; i++) {
		ptr.Objaddr = Core.read<uintptr_t>(ptr.Arrayaddr + 8 * i);  // 遍历数量次数
		//跳出
		if (ptr.Oneself == ptr.Objaddr || ptr.Objaddr <= 0x10000000 || ptr.Objaddr % 4 != 0 || ptr.Objaddr >= 0x10000000000)
			continue;
		
		int index = Core.read<uint32_t>(ptr.Objaddr + 0x2C);
		std::string className = GetName(ptr.Gname, index);
		const bool isCharacter = (className.find("Character") != std::string::npos) && (className.find("AI") == std::string::npos);//判断玩家
		const bool isAICharacter = (className.find("Character") != std::string::npos) && (className.find("AI") != std::string::npos);//判断AI
		const bool isItem = (className.find("Pickup_C") != std::string::npos); // 可拾取物资类名
		//const bool isBox = (className.find("NC_BP_Inventory_CarryBody_C") != std::string::npos); // 盒子NC_BP_Inventory_CarryBody_C   NC_BP_Inventory_DeadBody_C
		//const bool isItem = (className.find("Pickup_C") != std::string::npos); // 可拾取物资类名
		if (className.length() >= 50 || (className.find("BP_CharacterLODSystem_C") != std::string::npos)) {
			// 判断字符串长度大于大于50跳过 因为我们用到的类名没有那么长
			// BP_CharacterLODSystem_C 是 Unreal Engine 4（UE4）蓝图系统自动生成的类名
			//BP_ropertyReplicationCharacterHealth_C_PropertyReplicationActor_UniqueName 这个变量通常出现在 PropertyReplication 相关系统中，用于在客户端和服务器之间同步属性（例如血量、护甲、状态）
			continue;
		}

		if (!isCharacter && !isItem && !isAICharacter) {
			continue; // 跳过非目标类
		}
		if (isCharacter || isAICharacter) {
			ptr.Object = Core.read<uintptr_t>(Core.read<uintptr_t>(ptr.Objaddr + 0x3F0) + 0x110);
		}
		else  if (isItem) {
			ptr.Object = Core.read<uintptr_t>(ptr.Objaddr + 0x17A0);
		}
		else { ptr.Object = Core.read<uintptr_t>(ptr.Objaddr + 0x190); }
		Core.read(ptr.Object + 0x230, &ptr.Pos, 12);
		
		int 头盔等级 = Core.read<int>(Core.read<uintptr_t>(Core.read<uintptr_t>(ptr.Objaddr + 0x1F50) + 0x1E8) + 0x30);
		int 护甲等级 = Core.read<int>(Core.read<uintptr_t>(Core.read<uintptr_t>(ptr.Objaddr + 0x1F50) + 0x1E8) + 0xF0);
		int 手持武器 = Core.read<int>(Core.read<uintptr_t>(ptr.Objaddr + 0x1558) + 0x820);
		int 物资价值 = Core.read<int>(Core.read<uintptr_t>(ptr.Objaddr + 0xE40) + 0xd8);
		int 物资等级 = Core.read<int>(Core.read<uintptr_t>(ptr.Objaddr + 0xE40) + 0x68);
		int AI角色 = Core.read<int>(ptr.Objaddr + 0x9B0);//人机id

		uint8_t CharacterTags = Core.read<uint8_t>(Core.read<uintptr_t>(ptr.Objaddr + 0x3160));//角色标签
		//获取玩家名字 人机枚举
		UTF8 PlayerName[32] = "";
		uintptr_t APlayerState = Core.read<uintptr_t>(ptr.Objaddr + 0x3A0);//玩家状态
		getUTF8(PlayerName, Core.read<uintptr_t>(APlayerState + 0x398));//玩家名字
		int HeroID = Core.read<int>(APlayerState + 0x9B0);//干员ID

		ptr.TeamID = Core.read<int>(Core.read<uintptr_t>(ptr.Objaddr + 0xF00) + 0x118);
		uintptr_t temp1 = Core.read<uintptr_t>(ptr.Objaddr + 0xEF8);
		uintptr_t temp2 = Core.read<uintptr_t>(temp1 + 0x258);
		ptr.Health = Core.read<float>(temp2 + 0x68);
		UTF8 Playername[32] = "";
		getUTF8(Playername, Core.read<uintptr_t>(Core.read<uintptr_t>(ptr.Objaddr + 0x3A0) + 0x398));//名字
		uint32_t WeaponID = Core.read<uint32_t>(Core.read<uintptr_t>(ptr.Objaddr + 0x12F8) + 0x3DC);
		if (ptr.TeamID == MyTeamID && ptr.TeamID != 0)//MyTeamID
			continue;
		//过滤
		if (isAICharacter && ptr.Health <= 0 || !ptr.人机 && isAICharacter || isItem && 物资等级 < ptr.物资) { continue; }
		////计算方框
		三维 headPos = { ptr.Pos.x, ptr.Pos.y, ptr.Pos.z + 105.0f };
		三维 footPos = { ptr.Pos.x, ptr.Pos.y, ptr.Pos.z - 105.0f }; // 模拟脚下位置
		二维 head2D = WorldToScreen(headPos, viewInfo, abs_ScreenX, abs_ScreenY);
		二维 foot2D = WorldToScreen(footPos, viewInfo, abs_ScreenX, abs_ScreenY);

		if (head2D.x != 0 && foot2D.x != 0) {
			float boxHeight = foot2D.y - head2D.y;
			float boxWidth = boxHeight / 1.9f;//方框宽
			in.Box.t = head2D.y;
			in.Box.b = foot2D.y;
			in.Box.l = head2D.x - boxWidth / 2.0f;
			in.Box.r = head2D.x + boxWidth / 2.0f;
			in.Centre = in.Box.l + boxWidth / 2.0f;//中心点X
			in.CentreY = in.Box.t + boxHeight / 2.0f; //中心点Y
		}

		if (isCharacter)
		{
			in.Number++;
			人物颜色 = 红色;
		}
		else {
			人物颜色 = 白色;
		}

			 //计算距离
		float DistanceX = pow(ptr.Pos.x - ptr.MyPos.x, 2);
		float DistanceY = pow(ptr.Pos.y - ptr.MyPos.y, 2);
		float DistanceZ = pow(ptr.Pos.z - ptr.MyPos.z, 2);
		//强制转为int类型
		ptr.Distance = (int)sqrt(DistanceX + DistanceY + DistanceZ) * 0.01f;

		//获取骨骼
		ptr.Mesh = Core.read<uintptr_t>(Core.read<uintptr_t>(ptr.Objaddr + 0x968) + 0x0);
		ptr.Human = ptr.Mesh + 0x220;
		ptr.Bone = Core.read<uintptr_t>(ptr.Mesh + 0x728);
	

		//读取骨骼矩阵
		FTransform meshtrans;
		Core.read(ptr.Human, &meshtrans, 4 * 11);
		FMatrix c2wMatrix = TransformToMatrix(meshtrans);

		ImVec2 TempBone_Pos[15];

		for (int i = 0; i < 15; ++i) {
			FTransform temp_trans;
			Core.read(ptr.Bone + (db.boneTemp_Male[i] * 48), &temp_trans, 4 * 11);
			FMatrix boneMatrix = TransformToMatrix(temp_trans);
			三维 Pos = MarixToVector(MatrixMulti(boneMatrix, c2wMatrix));
			if (i == 0)
				Pos.z += 7; //头

			二维 ScreenPos = WorldToScreen(Pos, viewInfo, abs_ScreenX, abs_ScreenY);
			TempBone_Pos[i].x = ScreenPos.x;
			TempBone_Pos[i].y = ScreenPos.y;

		}
		

		if (isItem) {
			int high = Core.read<int>(ptr.Objaddr + 0xE90);
			int low = Core.read<int>(ptr.Objaddr + 0xE94);
			uint64_t iemid = (uint64_t)high * 10000 + low;
			std::string itemName = getItemName(iemid);

			// 原有逻辑：所有物品显示在屏幕上
			std::string s;
			s += itemName;
			s += "\n￥";
			s += std::to_string((int)物资价值);
			s += "[";
			s += std::to_string((int)ptr.Distance);
			s += "M]";
			auto Size = ImGui::CalcTextSize(s.c_str());
			绘制字体描边(30, in.Centre - (Size.x / 2), in.CentreY, get物品颜色(物资等级), s.c_str());

			// 新增逻辑：高价值物品存入列表（用于右上角窗口）
			// 修改 IsHighValueItem 调用，确保传递2个参数
			if (IsHighValueItem(物资价值, ptr.Distance)) {  // 确保传递value和distance
				// 修复narrowing错误，明确类型转换
				highValueItems.push_back({
					itemName,
					static_cast<int>(物资价值),
					static_cast<float>(ptr.Distance),  // 明确转换为float
					iemid  // uint64_t不需要转换
					});
			}

			continue;
		}





		//if (isItem) {
		//	int high = Core.read<int>(ptr.Objaddr + 0xE90);
		//	int low = Core.read<int>(ptr.Objaddr + 0xE94);
		//	uint64_t iemid = (uint64_t)high * 10000 + low;
		//	std::string s;
		//	s += getItemName(iemid);
		//	s += "\n￥";
		//	s += std::to_string((int)物资价值);
		//	s += "[";
		//	s += std::to_string((int)ptr.Distance);
		//	s += "M]";
		//	auto Size = ImGui::CalcTextSize(s.c_str());
		//	绘制字体描边(30, in.Centre - (Size.x / 2), in.CentreY, get物品颜色(物资等级), s.c_str());//26
		//	continue;
		//}






		if (ptr.Health <= 0 && ptr.盒子 && isCharacter && Playername[0] != '\0')
		{
			std::string s;
			//	s += Playername;
			//	s += "的盒子";
			//	s += "[";
			//	s += std::to_string((int)ptr.Distance);
			s += "真人盒子";
			auto Size = ImGui::CalcTextSize(s.c_str());
			绘制字体描边(25, in.Centre - (Size.x / 2), in.CentreY, 黄色, s.c_str());
			continue;//跳出当前循环，执行下次循环
		}



		//方框
		if (ptr.方框 && isCharacter) { Draws->AddRect(ImVec2(in.Box.l, in.Box.t), ImVec2(in.Box.r, in.Box.b), 人物颜色, { 0 }, 0, { 1.5f }); }


		//骨骼
		if (ptr.骨骼) {
			Initptr.DrawBone(TempBone_Pos, 人物颜色, { 2.0f });
		}

		if (ptr.距离 && isCharacter) {
			std::string s;
			s += std::to_string((int)ptr.Distance);
			s += "[M]";
			//s += std::to_string((int)ptr.TeamID);

			auto textSize = ImGui::CalcTextSize(s.c_str());
			绘制字体描边(26, in.Centre - textSize.x / 2, in.Box.b, ImColor(255, 255, 255), s.c_str());
		}


		if (ptr.射线 && isCharacter) { Draws->AddLine({ abs_ScreenX / 2, 0 }, { in.Centre,in.Box.t }, 人物颜色, { 1.5f }); }


		if (ptr.血量 && isCharacter) {
			

			DrawHealthBar(in.Centre, in.Box.t - 13, ptr.Health, 100);

		}




		if (ptr.头甲 && isCharacter) {
			std::string s;
			头盔等级 = getHelmetLevel(头盔等级);
			护甲等级 = getHelmetLevel(护甲等级); // 注意: 护甲应单独函数
			// 获取颜色
			ImColor 头盔颜色 = 获取等级颜色((int)头盔等级);
			ImColor 护甲颜色 = 获取等级颜色((int)护甲等级);
			// 分别绘制头盔和护甲等级（避免颜色混合）
			std::string 头文本 = "头:" + std::to_string((int)头盔等级);
			std::string 甲文本 = "甲:" + std::to_string((int)护甲等级);
			// 计算总文本宽度（用于居中）
			auto 总宽度 = ImGui::CalcTextSize((头文本 + " " + 甲文本).c_str()).x;
			float 起始X = in.Centre - (总宽度 / 2);
			float 起始Y = in.Box.t - 55 - ImGui::CalcTextSize("头:0").y;
			// 分别绘制（带描边）
			绘制字体描边(25, 起始X, 起始Y, 头盔颜色, 头文本.c_str());
			绘制字体描边(25, 起始X + ImGui::CalcTextSize(头文本.c_str()).x + 4, 起始Y, 护甲颜色, 甲文本.c_str());

		}



		/*if (ptr.头甲 && isCharacter) {
			std::string s;
			头盔等级 = getHelmetLevel(头盔等级);
			护甲等级 = getHelmetLevel(护甲等级);
			s += "头:";
			s += std::to_string((int)头盔等级);
			s += " ";
			s += "甲:";
			s += std::to_string((int)护甲等级);
			auto Size = ImGui::CalcTextSize(s.c_str());
			绘制字体描边(25, in.Centre - (Size.x / 2), in.Box.t - 55 - Size.y, 白色, s.c_str());
		}*/

		if (ptr.手持 && isCharacter) {
			std::string s;
			s += getChinese(手持武器);
			auto Size = ImGui::CalcTextSize(s.c_str());
			绘制字体描边(23, in.Centre - Size.x * 0.5f, in.Box.t - Size.y, 黄色, s.c_str());
		}

		if (ptr.名字 && isAICharacter || ptr.名字 && isCharacter) {
			if (isCharacter) {
				std::string heroName = getHeroName(HeroID);
					char extra[64] = "";

				sprintf(extra, "%d队%s %s", ptr.TeamID, heroName.c_str(), PlayerName);

				auto Size = ImGui::CalcTextSize(extra, 0, 25);
				绘制字体描边(25, in.Centre - (Size.x / 2), in.Box.t - 33 - Size.y, 白色, extra);
			}

			else {
				std::string extra = GetAICharacterTagNameById(CharacterTags);
				auto Size = ImGui::CalcTextSize(extra.c_str(), 0, 25);
				绘制字体描边(25, in.Centre - (Size.x / 2), in.Box.t - 15 - Size.y, 白色, extra.c_str());

			}


		}




		/*if (ptr.结构) {
			char Dbug[32];
			sprintf(Dbug, "%lx", ptr.Objaddr);
			auto Size = ImGui::CalcTextSize(Dbug);
			绘制字体描边(20, in.Centre - (Size.x / 2), in.Box.b + 20, ImColor(ImVec4(255 / 255.f, 255 / 255.f, 0 / 255.f, 0.95f)), Dbug);
			绘制字体描边(20, in.Centre - (Size.x / 2), in.Box.b + 40, ImColor(ImVec4(255 / 255.f, 255 / 255.f, 0 / 255.f, 0.95f)), className.c_str());


		}*/


	}//绘制循环结束	


 // 1. 格式化玩家数量
	char playerCountStr[32];
	snprintf(playerCountStr, sizeof(playerCountStr), "%d", in.Number);
	/*char playerCountStr[32];
	snprintf(playerCountStr, sizeof(playerCountStr), "%d", in.Number);*/
	// 2. 计算文字尺寸（使用指定字体大小）
	const float fontSize = 27.0f;
	ImVec2 textSize = ImGui::CalcTextSize(playerCountStr, nullptr, false, 0.0f);
	textSize.y = fontSize; // 修正高度（CalcTextSize 可能返回不精确的行高）

	// 3. 计算居中位置（屏幕上方 1/6 处）
	ImVec2 center(abs_ScreenX * 0.5f, abs_ScreenY * 0.0667f); // 1/6 ≈ 0.1667

	// 4. 背景矩形参数
	const float paddingX = 10.0f;
	const float paddingY = 6.0f;
	const float rounding = 5.0f;
	ImVec2 boxMin(center.x - textSize.x * 0.5f - paddingX, center.y - textSize.y * 0.5f - paddingY);
	ImVec2 boxMax(center.x + textSize.x * 0.5f + paddingX, center.y + textSize.y * 0.5f + paddingY);

	// 5. 绘制
	ImDrawList* drawList = ImGui::GetForegroundDrawList();
	drawList->AddRectFilled(boxMin, boxMax, IM_COL32(0, 255, 0, 255), rounding); // 半透明红色背景
	drawList->AddText(
		nullptr,                        // 使用默认字体
		fontSize,                       // 字号
		ImVec2(center.x - textSize.x * 0.5f, center.y - textSize.y * 0.5f),
		IM_COL32(255, 255, 255, 255),   // 白色文字
		playerCountStr
	);

}//绘图函数结束



//绘图函数 
void MyImGui::DrawBone(ImVec2* 骨骼, ImColor SkeletonDrawColor, float SkeletonDrawSize) {
	ImGui::GetForegroundDrawList()->AddCircle({ 骨骼[0].x, 骨骼[0].y }, 骨骼[1].y - 骨骼[0].y, SkeletonDrawColor, 50, { SkeletonDrawSize });
	ImGui::GetForegroundDrawList()->AddLine({ 骨骼[0].x, 骨骼[0].y }, { 骨骼[1].x, 骨骼[1].y }, SkeletonDrawColor, { SkeletonDrawSize });
	ImGui::GetForegroundDrawList()->AddLine({ 骨骼[1].x, 骨骼[1].y }, { 骨骼[2].x, 骨骼[2].y }, SkeletonDrawColor, { SkeletonDrawSize });
	ImGui::GetForegroundDrawList()->AddLine({ 骨骼[1].x, 骨骼[1].y }, { 骨骼[3].x,骨骼[3].y }, SkeletonDrawColor, { SkeletonDrawSize });
	ImGui::GetForegroundDrawList()->AddLine({ 骨骼[1].x, 骨骼[1].y }, { 骨骼[4].x,骨骼[4].y }, SkeletonDrawColor, { SkeletonDrawSize });
	ImGui::GetForegroundDrawList()->AddLine({ 骨骼[3].x, 骨骼[3].y }, { 骨骼[5].x,骨骼[5].y }, SkeletonDrawColor, { SkeletonDrawSize });
	ImGui::GetForegroundDrawList()->AddLine({ 骨骼[4].x, 骨骼[4].y }, { 骨骼[6].x, 骨骼[6].y }, SkeletonDrawColor, { SkeletonDrawSize });
	ImGui::GetForegroundDrawList()->AddLine({ 骨骼[5].x, 骨骼[5].y }, { 骨骼[7].x,骨骼[7].y }, SkeletonDrawColor, { SkeletonDrawSize });
	ImGui::GetForegroundDrawList()->AddLine({ 骨骼[6].x, 骨骼[6].y }, { 骨骼[8].x,骨骼[8].y }, SkeletonDrawColor, { SkeletonDrawSize });
	ImGui::GetForegroundDrawList()->AddLine({ 骨骼[2].x, 骨骼[2].y }, { 骨骼[9].x, 骨骼[9].y }, SkeletonDrawColor, { SkeletonDrawSize });
	ImGui::GetForegroundDrawList()->AddLine({ 骨骼[2].x, 骨骼[2].y }, { 骨骼[10].x,骨骼[10].y }, SkeletonDrawColor, { SkeletonDrawSize });
	ImGui::GetForegroundDrawList()->AddLine({ 骨骼[9].x, 骨骼[9].y }, { 骨骼[11].x,骨骼[11].y }, SkeletonDrawColor, { SkeletonDrawSize });
	ImGui::GetForegroundDrawList()->AddLine({ 骨骼[10].x, 骨骼[10].y }, { 骨骼[12].x,骨骼[12].y }, SkeletonDrawColor, { SkeletonDrawSize });
	ImGui::GetForegroundDrawList()->AddLine({ 骨骼[11].x, 骨骼[11].y }, { 骨骼[13].x,骨骼[13].y }, SkeletonDrawColor, { SkeletonDrawSize });
	ImGui::GetForegroundDrawList()->AddLine({ 骨骼[12].x, 骨骼[12].y }, { 骨骼[14].x,骨骼[14].y }, SkeletonDrawColor, { SkeletonDrawSize });
}
