#include <random>
#include <stdlib.h>
#include <unistd.h>
#include <linux/input.h>
#include <time.h>
#include <pthread.h>
#include <thread>
#include <unistd.h>
#include <linux/uinput.h>
#include <touch.h>
#include <draw.h>
#include <timer.h>
#include <dirent.h>
#include <fcntl.h>
#include <set>

using namespace std;
using std::string;
float x_proportion, y_proportion;
TouchFinger Fingers[10]; 

int GetEventCount()
{
	DIR *dir = opendir("/dev/input/");
	dirent *ptr = NULL;
	int count = 0;
	while ((ptr = readdir(dir)) != NULL)
	{
		if (strstr(ptr->d_name, "event"))
			count++;
	}
	return count;
}

int GetEventId()
{
	int EventCount = GetEventCount();
	int *fdArray = (int *)malloc(EventCount * 4 + 4);
	int result;

	for (int i = 0; i < EventCount; i++)
	{
		char temp[128];
		sprintf(temp, "/dev/input/event%d", i);
		fdArray[i] = open(temp, O_RDWR | O_NONBLOCK);
	}
	int k = 0;
	input_event ev;
	
	while (1)
	{
		for (int i = 0; i < EventCount; i++)
		{
			memset(&ev, 0, sizeof(ev));
			read(fdArray[i], &ev, sizeof(ev));
			if (ev.type == EV_ABS)
			{
				free(fdArray);
				return i;
			}
		}
		usleep(100);
	}
}

int fb,dev_fd;
int last_slot = -1;
bool touch_status = false;
bool TouchLock = false;
int global_tracking_id = 0;
bool bLock = false;

void Upload(int slot) {
    if (dev_fd <= 0) {
		bLock = false;
		return;
	}
    
    auto& Finger = Fingers[slot];
    if (Fingers[slot].status == FINGER_NO) {
		bLock = false;
        return;
	}
    
	int TimeBlock = 0;
	while (true) {
		if (!bLock || TimeBlock >= 200) {
			break;
		}
		TimeBlock ++;
	}
    
	bLock = true;
	
    struct input_event event[16];
    // 开始上传数据
	
    int32_t tmpCnt = 0;
    // 重置事件
    
    if (Finger.status != FINGER_UP) {
    	if (!touch_status)
		{
			touch_status = true;
			event[tmpCnt].type = EV_KEY;
			event[tmpCnt].code = BTN_TOUCH;
			event[tmpCnt].value = 1;
			tmpCnt++;
		}
		
        if (last_slot != slot)
		{
			event[tmpCnt].type = EV_ABS;
		    event[tmpCnt].code = ABS_MT_SLOT;
	        event[tmpCnt].value = slot;
		    tmpCnt++;
			last_slot = slot;
		}
		
	    event[tmpCnt].type = EV_ABS;
		event[tmpCnt].code = ABS_MT_TRACKING_ID;
	    event[tmpCnt].value = Finger.tracking_id;
		tmpCnt++;
		
		bool x_update = Finger.status == FINGER_X_UPDATE || Finger.status == FINGER_XY_UPDATE;
		bool y_update = Finger.status == FINGER_Y_UPDATE || Finger.status == FINGER_XY_UPDATE;
		
		if (x_update) {
		    event[tmpCnt].type = EV_ABS;
		    event[tmpCnt].code = ABS_MT_POSITION_X;
	        event[tmpCnt].value = Finger.x;
		    tmpCnt++;
	    }
		if (y_update) {
			event[tmpCnt].type = EV_ABS;
		    event[tmpCnt].code = ABS_MT_POSITION_Y;
	        event[tmpCnt].value = Finger.y;
		    tmpCnt++;
		}
		
    	event[tmpCnt].type = EV_SYN;
        event[tmpCnt].code = SYN_REPORT;
        event[tmpCnt].value = 0;
        tmpCnt++;
    } else {
        if (last_slot != slot)
		{
			event[tmpCnt].type = EV_ABS;
		    event[tmpCnt].code = ABS_MT_SLOT;
	        event[tmpCnt].value = slot;
		    tmpCnt++;
			last_slot = slot;
		}
		
		event[tmpCnt].type = EV_ABS;
	    event[tmpCnt].code = ABS_MT_TRACKING_ID;
        event[tmpCnt].value = -1;
	    tmpCnt++;
		
		event[tmpCnt].type = EV_SYN;
        event[tmpCnt].code = SYN_REPORT;
        event[tmpCnt].value = 0;
        tmpCnt++;
	
		touch_status = false;
    }
	
    for (int i = 0; i < tmpCnt; i ++) {
        event[i].time = Finger.time;
    }
   
    Finger.status = FINGER_NO;
       
	bLock = false;
	
	write(dev_fd, event, sizeof(struct input_event) * tmpCnt);
}
/*
void Touch_Down(int slot, float x,float y){
    auto& Finger = Fingers[slot];
	
    if (Finger.x != x && Finger.y != y)
		Finger.status = FINGER_XY_UPDATE;
	else if (Finger.x != x)
		Finger.status = FINGER_X_UPDATE;
	else if (Finger.y != y)
		Finger.status = FINGER_Y_UPDATE;
		
    Finger.x = x * x_proportion;
    Finger.y = y * y_proportion;
	
    Finger.tracking_id = slot;
    
    gettimeofday(&Finger.time, 0);
	
    Upload(slot);
}



void Touch_Up(int slot){
    auto& Finger = Fingers[slot];
	Finger.status = FINGER_UP;
    gettimeofday(&Finger.time, 0);
	Upload(slot);
}*/

timer loopAutoSleep;
ImGuiIO* io;

void HandleTouchEvent() {
    int input_id = GetEventId();
	char devicepath[64];

	sprintf(devicepath, "/dev/input/event%d", input_id);

    fb = open(devicepath, O_RDONLY | O_NONBLOCK);
	struct input_absinfo absX;
	struct input_absinfo absY;
	ioctl(fb, EVIOCGABS(ABS_MT_POSITION_X), &absX);
	ioctl(fb, EVIOCGABS(ABS_MT_POSITION_Y), &absY);
	float Width = absX.maximum + 1;
	float Height = absY.maximum + 1;
	
	int scr_x = abs_ScreenX;
    int scr_y = abs_ScreenY;
 
	if(scr_x > scr_y){
	    int t = scr_y;
	    scr_y = scr_x;
	    scr_x = t;
	}
    
    x_proportion = Width / scr_x;
    y_proportion = Height / scr_y;
    
    struct input_event in_ev, last_in_ev;

	int slot = 0, tracking_id = 0;
	int type, code, value;
	std::set<int> UpdatePointers;
	loopAutoSleep.SetFps(1200);
    loopAutoSleep.AotuFPS_init();
	
    
	//GrabTouchScreen();

	    while (true) {
		UpdatePointers.clear();
		
		while (true) {
				
    	    io = &ImGui::GetIO();    
    	    read(fb, &in_ev, sizeof(in_ev));

    	    if (in_ev.code != SYN_REPORT) {
    			type = in_ev.type;
    			code = in_ev.code;
    			value = in_ev.value;
    			
    			auto& Finger = Fingers[slot];
    			Finger.time = in_ev.time;

                if (code == ABS_MT_POSITION_Y) {
                    if (slot == 0) {
                        if (displayInfo.orientation == 0)
                            io->MousePos.y = value / y_proportion;
                        else if (displayInfo.orientation == 1)
                            io->MousePos.x = value / y_proportion;
                        else
                            io->MousePos.x = scr_y - value / y_proportion;
                    }

                    Finger.y = value;
                    Finger.status |= FINGER_Y_UPDATE;
                    UpdatePointers.insert(slot);
                }
                else if (code == ABS_MT_POSITION_X) {
                    if (slot == 0) {
                        if (displayInfo.orientation == 0)
                            io->MousePos.x = value / x_proportion;
                        else if (displayInfo.orientation == 1)
                            io->MousePos.y = scr_x - value / x_proportion;
                        else
                            io->MousePos.y = value / x_proportion;
                    }
					

    		        Finger.x = value;
    		        Finger.status |= FINGER_X_UPDATE;		
    		        UpdatePointers.insert(slot);		   
    			} else if(code == ABS_MT_TRACKING_ID) {
    			    if (value == -1){
    			        if(slot == 0)
    			            io->MouseDown[0] = false;
    			        			
    			        Finger.status = FINGER_UP;
    			        UpdatePointers.insert(slot);		     
    			    } else {
    			        if(slot == 0)
    			            io->MouseDown[0] = true;
    			        
    			        Finger.tracking_id = global_tracking_id;
    					global_tracking_id ++;
    			    }		  			    
    			} else if(code == ABS_MT_SLOT) {
    			    slot = value;
    			}
				
    		} else {
                for (std::set<int>::iterator i=UpdatePointers.begin();i!=UpdatePointers.end();i++) {
                     Upload(*i); //注意指针运算符
                }
    		    break;	
    		}
		}
	    loopAutoSleep.AotuFPS();
	}
}

char *randstr(char *str, const int len)
{
    srand(time(NULL));
    int i;
    for (i = 0; i < len; ++i)
    {
        switch ((rand() % 3))
        {
        case 1:
            str[i] = 'A' + rand() % 26;
            break;
        case 2:
            str[i] = 'a' + rand() % 26;
            break;
        default:
            str[i] = '0' + rand() % 10;
            break;
        }
    }
    str[++i] = '\0';
    return str;
}


bool GrabTouchScreen() {
	if (ioctl(fb, 0x40044590uLL, 1))
	{
		char *v1 = strerror(*__errno());
		printf("创建触摸屏失败 %s.\n", v1);
		return false;
        exit(0);
	} else {
		
		// 创建驱动	
		static int uinp_fd;
		struct uinput_user_dev uinp;
		struct input_event event;
		
		uinp_fd = open("/dev/uinput", O_WRONLY|O_NONBLOCK);
		
		if (uinp_fd == 0) { // 打开uinput
			printf("无法打开 /dev/uinput 请检查是否有root权限\n");
			return false;
		}
		
		dev_fd = uinp_fd; 
		
		memset(&uinp, 0, sizeof(uinp));

        char name[16];
        
    	strncpy(uinp.name, randstr(name, 15), UINPUT_MAX_NAME_SIZE);
    	uinp.id.vendor = rand() % 3;
        uinp.id.product = rand() % 3;
        uinp.id.version = (rand() % 2 == 0 ? 0 : 0x100);
        
        ioctl(uinp_fd, UI_SET_PHYS, name);
    	struct input_absinfo absX;
    	struct input_absinfo absY;
    	ioctl(fb, EVIOCGABS(ABS_MT_POSITION_X), &absX);
    	ioctl(fb, EVIOCGABS(ABS_MT_POSITION_Y), &absY);
    
        uinp.absmin[ABS_MT_SLOT] = 0;
		uinp.absmax[ABS_MT_SLOT] = 9;
		uinp.absmin[ABS_MT_TRACKING_ID] = 0;
		uinp.absmax[ABS_MT_TRACKING_ID] = 65535;//按键码ID累计叠加最大值

        uinp.absmin[ABS_MT_POSITION_X] = 0;
        uinp.absmax[ABS_MT_POSITION_X] = absX.maximum;

        uinp.absmin[ABS_MT_POSITION_Y] = 0;
        uinp.absmax[ABS_MT_POSITION_Y] = absY.maximum;

        ioctl(uinp_fd, UI_SET_PROPBIT, INPUT_PROP_DIRECT);
    
        ioctl(uinp_fd, UI_SET_EVBIT, EV_ABS);
		ioctl(uinp_fd, UI_SET_ABSBIT, ABS_MT_SLOT);
        ioctl(uinp_fd, UI_SET_ABSBIT, ABS_MT_POSITION_X);
        ioctl(uinp_fd, UI_SET_ABSBIT, ABS_MT_POSITION_Y);
        ioctl(uinp_fd, UI_SET_ABSBIT, ABS_MT_TRACKING_ID);
    
        ioctl(uinp_fd, UI_SET_EVBIT, EV_SYN);
    
       // 随机注册
        for (int ii = 0; ii < 41; ii++) {
            int i = rand() % 41;
            if (i == 0) { ioctl(uinp_fd, UI_SET_ABSBIT, ABS_X); }
            if (i == 1) { ioctl(uinp_fd, UI_SET_ABSBIT, ABS_Y); }
            if (i == 2) { ioctl(uinp_fd, UI_SET_ABSBIT, ABS_Z); }
    
            if (i == 3) { ioctl(uinp_fd, UI_SET_ABSBIT, ABS_RX); }
            if (i == 4) { ioctl(uinp_fd, UI_SET_ABSBIT, ABS_RY); }
            if (i == 5) { ioctl(uinp_fd, UI_SET_ABSBIT, ABS_RZ); }
    
            if (i == 6) { ioctl(uinp_fd, UI_SET_ABSBIT, ABS_THROTTLE); }
            if (i == 7) { ioctl(uinp_fd, UI_SET_ABSBIT, ABS_RUDDER); }
            if (i == 8) { ioctl(uinp_fd, UI_SET_ABSBIT, ABS_WHEEL); }
            if (i == 9) { ioctl(uinp_fd, UI_SET_ABSBIT, ABS_GAS); }
    
    
            if (i == 10) { ioctl(uinp_fd, UI_SET_ABSBIT, ABS_BRAKE); }
            if (i == 11) { ioctl(uinp_fd, UI_SET_ABSBIT, ABS_HAT0Y); }
            if (i == 12) { ioctl(uinp_fd, UI_SET_ABSBIT, ABS_HAT1X); }
            if (i == 13) { ioctl(uinp_fd, UI_SET_ABSBIT, ABS_HAT1Y); }
            if (i == 14) { ioctl(uinp_fd, UI_SET_ABSBIT, ABS_HAT2X); }
            if (i == 15) { ioctl(uinp_fd, UI_SET_ABSBIT, ABS_HAT2Y); }
            if (i == 16) { ioctl(uinp_fd, UI_SET_ABSBIT, ABS_HAT3X); }
            if (i == 17) { ioctl(uinp_fd, UI_SET_ABSBIT, ABS_HAT3Y); }
            if (i == 18) { ioctl(uinp_fd, UI_SET_ABSBIT, ABS_PRESSURE); }
            if (i == 19) { ioctl(uinp_fd, UI_SET_ABSBIT, ABS_DISTANCE); }
            if (i == 20) { ioctl(uinp_fd, UI_SET_ABSBIT, ABS_TILT_X); }
            if (i == 21) { ioctl(uinp_fd, UI_SET_ABSBIT, ABS_TILT_Y); }
            if (i == 22) { ioctl(uinp_fd, UI_SET_ABSBIT, ABS_TOOL_WIDTH); }
            if (i == 23) { ioctl(uinp_fd, UI_SET_ABSBIT, ABS_VOLUME); }
            if (i == 24) { ioctl(uinp_fd, UI_SET_ABSBIT, ABS_MISC); }
    
            if (i == 25) { ioctl(uinp_fd, UI_SET_ABSBIT, ABS_MT_TOUCH_MAJOR); }
            if (i == 26) { ioctl(uinp_fd, UI_SET_ABSBIT, ABS_MT_TOUCH_MINOR); }
            if (i == 27) { ioctl(uinp_fd, UI_SET_ABSBIT, ABS_MT_WIDTH_MAJOR); }
            if (i == 28) { ioctl(uinp_fd, UI_SET_ABSBIT, ABS_MT_WIDTH_MINOR); }
            if (i == 29) { ioctl(uinp_fd, UI_SET_ABSBIT, ABS_MT_ORIENTATION); }
            if (i == 30) { ioctl(uinp_fd, UI_SET_ABSBIT, ABS_MT_POSITION_X); }
            if (i == 31) { ioctl(uinp_fd, UI_SET_ABSBIT, ABS_MT_POSITION_Y); }
            if (i == 32) { ioctl(uinp_fd, UI_SET_ABSBIT, ABS_MT_TOOL_TYPE); }
            if (i == 33) { ioctl(uinp_fd, UI_SET_ABSBIT, ABS_MT_BLOB_ID); }
            if (i == 34) { ioctl(uinp_fd, UI_SET_ABSBIT, ABS_MT_TRACKING_ID); }
            if (i == 35) { ioctl(uinp_fd, UI_SET_ABSBIT, ABS_MT_PRESSURE); }
            if (i == 36) { ioctl(uinp_fd, UI_SET_ABSBIT, ABS_MT_DISTANCE); }
            if (i == 37) { ioctl(uinp_fd, UI_SET_ABSBIT, ABS_MT_TOOL_X); }
            if (i == 38) { ioctl(uinp_fd, UI_SET_ABSBIT, ABS_MT_TOOL_Y); }
            if (i == 39) { ioctl(uinp_fd, UI_SET_ABSBIT, ABS_MAX); }
            if (i == 40) { ioctl(uinp_fd, UI_SET_ABSBIT, ABS_CNT); } 
        }

        uint8_t *bits = NULL;
        ssize_t bits_size = 0;
        int res, j, k;
        while (1) {
            res = ioctl(fb, EVIOCGBIT(EV_KEY, bits_size), bits);
            if (res < bits_size)
                break;
            bits_size = res + 16;
            bits = (uint8_t *) realloc(bits, bits_size * 2);
            if (bits == NULL)
                exit(1);
        }

        for (j = 0; j < res; j++) {
            for (k = 0; k < 8; k++)
                if (bits[j] & 1 << k) {

                    if (j * 8 + k < 249 & j * 8 + k > -1) {
                        ioctl(uinp_fd, UI_SET_KEYBIT, j * 8 + k);
                    }

                }
        }

        ioctl(uinp_fd, UI_SET_EVBIT, EV_KEY);
    
		write(uinp_fd, &uinp, sizeof(uinp));//真实屏幕的触摸
		ioctl(uinp_fd, UI_DEV_CREATE);
	}
	return true;
}

//bool GrabTouchScreen() {
//    // 创建驱动
//    int uinp_fd; // 修改为局部变量
//    struct uinput_user_dev uinp; // 确保在此作用域声明
//
//    uinp_fd = open("/dev/uinput", O_WRONLY | O_NONBLOCK);
//    if (uinp_fd <= 0) {
//        printf("无法打开 /dev/uinput 请检查是否有root权限\n");
//        return false;
//    }
//
//    memset(&uinp, 0, sizeof(uinp)); // 必须初始化
//
//    // 克隆真实设备信息
//    struct input_id real_id;
//    ioctl(fb, EVIOCGID, &real_id);
//    uinp.id.bustype = BUS_USB;
//    uinp.id.vendor = real_id.vendor;
//    uinp.id.product = real_id.product;
//    uinp.id.version = 0x0110;
//
//    char real_name[UINPUT_MAX_NAME_SIZE];
//    ioctl(fb, EVIOCGNAME(sizeof(real_name)), real_name);
//    strncpy(uinp.name, real_name, UINPUT_MAX_NAME_SIZE);
//
//    // ... 其他代码 ...
//
//    // 创建后立即隐藏设备节点
//    ioctl(uinp_fd, UI_DEV_CREATE);
//    char dev_node[64];
//    ioctl(uinp_fd, UI_GET_SYSNAME(sizeof(dev_node)), dev_node);
//    unlink(dev_node);
//
//    return true;
//}