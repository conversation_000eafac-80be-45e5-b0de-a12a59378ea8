#include <cmath>
//#include <string>  // 确保 std::string 可用
#pragma once
#ifndef MEMBER_H
#define MENBER_H

// 二维成员变量
//struct 二维 {
//    float x;
//    float y;
//	二维() {
//        this->x = 0;
//        this->y = 0;
//    }
//    二维(float x, float y) {
//        this->x = x;
//        this->y = y;
//    }
//};

// 三维成员变量
//struct 三维 {
//    float x;
//    float y;
//    float z;
//	三维() {
//        this->x = 0;
//        this->y = 0;
//        this->z = 0;
//    }
//    三维(float x, float y, float z) {
//        this->x = x;
//        this->y = y;
//        this->z = z;
//    }
//};
struct 二维 {
    float x;
    float y;
    二维() : x(0), y(0) {}
    二维(float _x, float _y) : x(_x), y(_y) {}

    // 加法
    二维 operator+(const 二维& rhs) const {
        return 二维(x + rhs.x, y + rhs.y);
    }

    // 减法
    二维 operator-(const 二维& rhs) const {
        return 二维(x - rhs.x, y - rhs.y);
    }

    // 乘以标量
    二维 operator*(float rhs) const {
        return 二维(x * rhs, y * rhs);
    }
};

struct 三维 {
    float x;
    float y;
    float z;

    三维() : x(0), y(0), z(0) {}
    三维(float x, float y, float z) : x(x), y(y), z(z) {}

    三维 operator-(const 三维& other) const {
        return 三维(x - other.x, y - other.y, z - other.z);
    }
};


//四维成员变量
struct 四维{
	float l;
	float r;
	float t;
	float b;
};



struct FMatrix
{
    float M[4][4];
};

//和四维一样
struct Quat
{
    float x;
    float y;
    float z;
    float w;
};

struct FTransform
{
    Quat Rotation;
    三维 Translation;
    //float chunk;
    三维 Scale3D;
};

struct BoneStruct {
    三维 Pos;
	二维 ScreenPos;
};
struct D3DXMATRIX
{
	float _11;
	float _12;
	float _13;
	float _14;
	float _21;
	float _22;
	float _23;
	float _24;
	float _31;
	float _32;
	float _33;
	float _34;
	float _41;
	float _42;
	float _43;
	float _44;
};


struct FRotator {
    float Pitch;
    float Yaw;
    float Roll;
    
    
};


//// 定义物品结构体
//struct AdvancedItem {
//    std::string name;
//    int value;
//    float distance;
//    uint64_t id;
//};


struct MinimalViewInfo {
    三维 Location;
    FRotator Rotation;
    float FOV;
};

//字符转换
typedef char UTF8;
typedef unsigned short UTF16;

//骨骼计算 使用内联关键字告诉编译器火速给我把代码直接放过去,而不是调用
inline void Get_Bone(三维 obj, float matrix[16],二维 Screen,二维 *Coords)	
{
    float camear = matrix[3] * obj.x + matrix[7] * obj.y + matrix[11] * obj.z + matrix[15];
    Coords->x = (Screen.x / 2) + (matrix[0] * obj.x + matrix[4] * obj.y + matrix[8] * obj.z + matrix[12]) / camear * (Screen.x / 2);
    Coords->y = (Screen.y / 2) - (matrix[1] * obj.x + matrix[5] * obj.y + matrix[9] * obj.z + matrix[13]) / camear * (Screen.y / 2);
}
inline 三维 MarixToVector(FMatrix matrix)
{
    return 三维(matrix.M[3][0], matrix.M[3][1], matrix.M[3][2]);
}

inline FMatrix MatrixMulti(FMatrix m1, FMatrix m2)
{
    FMatrix matrix = FMatrix();
    for (int i = 0; i < 4; i++)
    {
        for (int j = 0; j < 4; j++)
        {
            for (int k = 0; k < 4; k++)
            {
                matrix.M[i][j] += m1.M[i][k] * m2.M[k][j];
            }
        }
    }
    return matrix;
}
inline FMatrix TransformToMatrix(const FTransform& transform)
{
    FMatrix matrix;

    float x = transform.Rotation.x;
    float y = transform.Rotation.y;
    float z = transform.Rotation.z;
    float w = transform.Rotation.w;

    float x2 = x + x, y2 = y + y, z2 = z + z;
    float xx = x * x2, yy = y * y2, zz = z * z2;
    float xy = x * y2, xz = x * z2, yz = y * z2;
    float wx = w * x2, wy = w * y2, wz = w * z2;

    float sx = transform.Scale3D.x;
    float sy = transform.Scale3D.y;
    float sz = transform.Scale3D.z;

    matrix.M[0][0] = (1.0f - (yy + zz)) * sx;
    matrix.M[0][1] = (xy + wz) * sx;
    matrix.M[0][2] = (xz - wy) * sx;
    matrix.M[0][3] = 0.0f;

    matrix.M[1][0] = (xy - wz) * sy;
    matrix.M[1][1] = (1.0f - (xx + zz)) * sy;
    matrix.M[1][2] = (yz + wx) * sy;
    matrix.M[1][3] = 0.0f;

    matrix.M[2][0] = (xz + wy) * sz;
    matrix.M[2][1] = (yz - wx) * sz;
    matrix.M[2][2] = (1.0f - (xx + yy)) * sz;
    matrix.M[2][3] = 0.0f;

    matrix.M[3][0] = transform.Translation.x;
    matrix.M[3][1] = transform.Translation.y;
    matrix.M[3][2] = transform.Translation.z;
    matrix.M[3][3] = 1.0f;

    return matrix;
}




inline FMatrix RotatorToMatrix(FRotator rotation) {
    float radPitch = rotation.Pitch * M_PI / 180.0f;
    float radYaw = rotation.Yaw * M_PI / 180.0f;
    float radRoll = rotation.Roll * M_PI / 180.0f;
    
    float SP = sinf(radPitch);
    float CP = cosf(radPitch);
    float SY = sinf(radYaw);
    float CY = cosf(radYaw);
    float SR = sinf(radRoll);
    float CR = cosf(radRoll);
    
    FMatrix matrix;
    
    matrix.M[0][0] = (CP * CY);
    matrix.M[0][1] = (CP * SY);
    matrix.M[0][2] = (SP);
    matrix.M[0][3] = 0;
    
    matrix.M[1][0] = (SR * SP * CY - CR * SY);
    matrix.M[1][1] = (SR * SP * SY + CR * CY);
    matrix.M[1][2] = (-SR * CP);
    matrix.M[1][3] = 0;
    
    matrix.M[2][0] = (-(CR * SP * CY + SR * SY));
    matrix.M[2][1] = (CY * SR - CR * SP * SY);
    matrix.M[2][2] = (CR * CP);
    matrix.M[2][3] = 0;
    
    matrix.M[3][0] = 0;
    matrix.M[3][1] = 0;
    matrix.M[3][2] = 0;
    matrix.M[3][3] = 1;
    
    return matrix;
}


inline 二维 WorldToScreen(三维 worldLocation,MinimalViewInfo viewInfo,int width, int height) {

    FMatrix tempMatrix = RotatorToMatrix(viewInfo.Rotation);
    三维 vAxisX(tempMatrix.M[0][0], tempMatrix.M[0][1], tempMatrix.M[0][2]);
    三维 vAxisY(tempMatrix.M[1][0], tempMatrix.M[1][1], tempMatrix.M[1][2]);
    三维 vAxisZ(tempMatrix.M[2][0], tempMatrix.M[2][1], tempMatrix.M[2][2]);
    三维 Delta = { worldLocation.x - viewInfo.Location.x, worldLocation.y - viewInfo.Location.y, worldLocation.z - viewInfo.Location.z };
    三维 vTransformed = {
        Delta.x * vAxisY.x + Delta.y * vAxisY.y + Delta.z * vAxisY.z,
        Delta.x * vAxisZ.x + Delta.y * vAxisZ.y + Delta.z * vAxisZ.z,
        Delta.x * vAxisX.x + Delta.y * vAxisX.y + Delta.z * vAxisX.z
    };

    // 读取FOV

    float fov = viewInfo.FOV;
    float screenCenterX = (width / 2.0f);
    float screenCenterY = (height / 2.0f);

    if (vTransformed.z < 1) {
        vTransformed.z = 1;
    }


    return 二维(
        (screenCenterX + vTransformed.x * (screenCenterX / tanf(fov * ((float)M_PI / 360.0f))) / vTransformed.z),
        (screenCenterY - vTransformed.y * (screenCenterX / tanf(fov * ((float)M_PI / 360.0f))) / vTransformed.z)
    );

}

#include <imgui.h>

// 计算向量长度平方
inline float 长度平方(const 二维& v) {
    return v.x * v.x + v.y * v.y;
}

// 二维转ImVec2
inline ImVec2 ToImVec2(const 二维& v) {
    return ImVec2(v.x, v.y);
}

#endif
