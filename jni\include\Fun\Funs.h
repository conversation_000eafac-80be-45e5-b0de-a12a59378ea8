#ifndef FUNS_H
#define FUNS_H
#include <Member.h>
#include <string.h>
#include <string>  // 确保 std::string 可用
//#include <string>  // 确保 std::string 可用
using namespace std;

//从C++ 11开始引入在结构体里面初始化成员变量

//基本数据的结构体成员
struct data{
	char *Kernel_drive_path;
	//地址等数据
	pid_t pid = -1;
	uintptr_t libUE4,Gname;
	uintptr_t Matrixs,Uworld,Arrayaddr,Oneself;
	int Count = 0;
	uintptr_t Objaddr,Object,Mesh,Human,BoneName,Bone,controller,Gworld,mode;
	//我的数据或者单个数据
	int Shoot;//开火
	int zoomin;//开镜
	三维 MyPos;//我的坐标
	三维 MyMove;
	三维 Pos;//敌人坐标
	三维 Move;
	float Matrix[16];//矩阵
	//对象数据或者多个获取
	int Distance,TeamID;//距离 队伍编号
	float Health;//血量
	float MaxHealth;//最大血量
	char Name[32];//名字
    int 触发方式;
	int 瞄准部位;
	int 自瞄方式;
	int 模式=1;
	int 物资;
	//imgui控件
    bool 自瞄圈圈;	
	bool 血量;
	bool 方框;
	bool 距离;
	bool 队友;
	bool 射线;
	bool 名字;
	bool 骨骼;
	bool 人机;
	bool 结构;
	bool 头甲;
	bool 手持;
	bool 盒子;
	bool 相机;
	bool 干员;
	bool 无后;
	bool 雷达;
	bool 战场模式;
};


// 完整的骨骼数据结构（包含骨骼索引和对应骨骼信息）
struct drawbone {
	uint32_t boneTemp_Male[15] = { 31,30,1,34,6,35,7,36,8,58,62,59,63,60,64 };
	BoneStruct bone[15];
};

//绘制/绘图的结构体成员
struct drawing{
	int Number = 0;//人数
	四维 Box;//敌人方框
	二维 ScreenPos;//敌人在转为屏幕的坐标
	float Centre;//中心
	float CentreY;
	float w;
};


struct sett{
	bool initialize = false;//初始化或者开始读取
	bool stop = true;//是否结束程序
	float *threadtime;//线程时间
	char init[32] = "initialize";
};

struct AdvancedItem {
	std::string name;
	int value;
	float distance;
	uint64_t id;
};

//创建结构体成员 实例化对象
extern struct data ptr;
extern struct drawing in;
extern struct sett settings;
extern struct drawbone db;


//自瞄结构体 //创建足够大的数组,防止溢出,一般52足够了,超出直接完蛋
struct collimation{
	int AimCount = 0;//自瞄人数
	三维 ObjAim[52];//敌人坐标
	//三维 AimMove[52];//向量
	二维 ObjScreenPos[52];//屏幕坐标
    float ScreenDistance[52];//敌人到屏幕中心距离
	float AimDistance[52];//距离
    //float Health[52];//血量
};

//自瞄指针
extern struct collimation *Aim;
extern int Aim_i;
extern 二维 *EnemyPos;
#endif
